name: Playwright Tests

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  workflow_dispatch:

jobs:
  test:
    name: Run Playwright Tests
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Install Playwright browsers
        run: npx playwright install --with-deps
      
      - name: Create .env.test file
        run: |
          echo "TEST_BASE_URL=${{ secrets.TEST_BASE_URL || 'https://mindlens.balanceflo.ai' }}" > .env.test
          echo "TEST_USER_EMAIL=${{ secrets.TEST_USER_EMAIL || '<EMAIL>' }}" >> .env.test
          echo "TEST_USER_PASSWORD=${{ secrets.TEST_USER_PASSWORD || '123456' }}" >> .env.test
          echo "API_URL=${{ secrets.API_URL || 'http://localhost:1337' }}" >> .env.test
      
      - name: Run Playwright tests
        run: npx playwright test
      
      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: playwright-report
          path: |
            playwright-report/
            test-results/
            tests/screenshots/
          retention-days: 30
