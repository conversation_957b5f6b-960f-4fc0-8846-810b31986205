import { IGeneratedReport } from '../SelfAssessment/Report/Report';
import { Box, Stack } from '@mui/material';
import MLTypography from '../../components/ui/MLTypography/MLTypography';
import MLButton from '../../components/ui/MLButton/MLButton';
import ErgoJourneyImage from './assets/ErgoJourneyImage';
import Markdown from 'react-markdown'
import SurpriseIcon from '../../assets/icons/SurpriseIcon';
import PersonalReportIcon from '../../assets/icons/PersonalReportIcon';
import IdentifyIcon from '../../assets/icons/IdentifyIcon';
import { useGo } from '@refinedev/core';
import { useState } from 'react';
import AskAQuestion from './AskAQuestion';
import ErgoRisksAndRecommendations from '../SelfAssessment/Report Summary/components/ErgoRisksAndRecommendations';

interface ErgoScoreActionPlanProps {
  report: IGeneratedReport | undefined;
  currentPage: string | undefined;
  handleActionPlanComplete: (outcome: string, points: number) => void;
}

const ErgoScoreActionPlan = ({ report, currentPage, handleActionPlanComplete }: ErgoScoreActionPlanProps) => {
  const [isNeedHelpModalOpened, setIsNeedHelpModalOpened] = useState<boolean>(false);

  const go = useGo();

  return (
    <Stack>
      {report ? (
        <Stack gap={"30px"} >
          <Stack
            direction={{ xs: "column", md: "row" }}
            sx={{
              border: "0.5px solid #E0E0E0",
              borderRadius: "10px",
              padding: { xs: "15px", sm: "30px" },
              alignItems: "center",
              gap: "30px"
            }}
          >
            <Stack
              sx={{
                position: "relative",
              }}
            >
              <svg width="112" height="112">
                <circle
                  cx="56"
                  cy="56"
                  r="50"
                  stroke="#000"
                  strokeWidth="1"
                  fill="none"
                />
                <circle
                  cx="56"
                  cy="56"
                  r="50"
                  stroke={
                    report.generatedReport.ergoPostureScore >= 75 ? "#2FBB00" : // green
                      report.generatedReport.ergoPostureScore >= 50 && report.generatedReport.ergoPostureScore < 75 ? "#FFBB00" : // yellow
                        report.generatedReport.ergoPostureScore >= 30 && report.generatedReport.ergoPostureScore < 50 ? "#FF7A00" : // orange
                        "#C40000" // red
                  }
                  strokeWidth="8"
                  fill="none"
                  strokeDasharray="314"
                  strokeDashoffset={314 - (314 * (report.generatedReport.ergoPostureScore ? (report.generatedReport.ergoPostureScore + report.generatedReport.achievedActionScore) : 0)) / 100}
                  style={{ transition: "stroke-dashoffset 0.5s ease-in-out" }}
                  transform="rotate(-90, 56, 56)"
                />
              </svg>
              <Stack
                sx={{
                  width: "112px",
                  height: "112px",
                  position: "absolute",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <MLTypography
                  fontSize={40}
                  variant="h5"
                  fontWeight={400}
                >
                  {report.generatedReport.ergoPostureScore ? (report.generatedReport.ergoPostureScore + report.generatedReport.achievedActionScore) : 0}
                </MLTypography>
              </Stack>
            </Stack>
            <Stack gap="12px">
              <MLTypography
                variant='h1'
                fontSize={"30px"}
                fontWeight={600}
                lineHeight={1.2}
              >
                Your Ergo Score
              </MLTypography>
              <Markdown
                components={{
                  p: ({ children }) => (
                    <MLTypography
                      variant="body1"
                    >
                      {children}
                    </MLTypography>
                  ),
                }}
              >
                {report.generatedReport.ergonomicScoreText.split('\n')[0]}
              </Markdown>
            </Stack>
          </Stack>
          <Stack>
            {report.generatedReport.actionPlans.length > 0 &&
              <ErgoRisksAndRecommendations
                handleActionPlanComplete={handleActionPlanComplete}
                actionPlans={report?.generatedReport?.actionPlans}
                handleViewAll={() => go({ to: "/self-assessment" })}
              />
            }
          </Stack>
        </Stack>
      ) : (
        <Stack
          direction={{ xs: "column", md: "row" }}
          sx={{
            border: "0.5px solid #7856FF",
            borderRadius: "10px",
            paddingX: { xs: "15px", sm: "30px" },
            paddingY: { xs: "20px", sm: "30px" },
            gap: "15px",
          }}
        >
          <Stack
            sx={{
              width: { xs: "100%", md: "60%" },
            }}
          >
            <Stack
              sx={{
                alignItems: "flex-start",
                gap: "14px",
                marginBottom: "30px"
              }}
            >
              <MLTypography variant="h1" fontSize={{ xs: "24px", sm: "32px" }} fontWeight={600} lineHeight={1.2}>
                Begin your ergo jouney with a postural assessment
              </MLTypography>
            </Stack>
            <Stack
              sx={{
                alignItems: "flex-start",
                gap: "25px",
                marginBottom: { xs: "10px", sm: "60px" }
              }}
            >
              <Stack direction={"row"} gap={"20px"} alignItems={"center"}>
                <Stack width={"35px"}>
                  <IdentifyIcon />
                </Stack>
                <Stack width={"95%"}>
                  <MLTypography variant="body1" fontSize={"16px"} fontWeight={400} lineHeight={1.2}>
                    Identify your high risk work habits.
                  </MLTypography>
                </Stack>
              </Stack>
              <Stack direction={"row"} gap={"20px"} alignItems={"center"}>
                <Stack width={"35px"}>
                  <PersonalReportIcon />
                </Stack>
                <Stack width={"95%"}>
                  <MLTypography variant="body1" fontSize={"16px"} fontWeight={400} lineHeight={1.2}>
                    Personalised report to improve setup and posture.
                  </MLTypography>
                </Stack>
              </Stack>
              <Stack direction={"row"} gap={"20px"} alignItems={"center"}>
                <Stack width={"35px"}>
                  <SurpriseIcon />
                </Stack>
                <Stack width={"95%"}>
                  <MLTypography variant="body1" fontSize={"16px"} fontWeight={400} lineHeight={1.2}>
                    Complete and unlock a surprise for yourself!
                  </MLTypography>
                </Stack>
              </Stack>
            </Stack>
            <Stack gap={"15px"} display={{ xs: "none", sm: "flex" }}>
              <MLButton
                onClick={() => {
                  { currentPage ? go({ to: "/self-assessment/new" }) : go({ to: "/self-assessment/getting-started" }) }
                }}
                variant="contained"
                color="secondary"
                sx={{
                  paddingX: "24px",
                  textTransform: 'uppercase',
                  alignSelf: 'flex-start',
                  backgroundColor: '#DFFF32',
                }}
              >
                {currentPage ? "Continue Assessment" : "Start Assessment"}
              </MLButton>
              <MLTypography variant="body1" fontSize={"12px"} fontWeight={500} lineHeight={1.2}>
                Invest <span style={{ fontWeight: 700 }}>10 mins</span> for your ergonomic wellbeing
              </MLTypography>
            </Stack>
          </Stack>
          <Stack
            sx={{
              width: { xs: "100%", md: "40%" },
              maxWidth: "480px",
              maxHeight: "350px"
            }}
          >
            <ErgoJourneyImage />
          </Stack>
          <Stack
            display={{ xs: "flex", sm: "none" }}
            sx={{
              justifyContent: "center",
              alignItems: "center",
              gap: "15px",
              marginTop: "20px",
            }}
          >
            <MLButton
              onClick={() => {
                { currentPage ? go({ to: "/self-assessment/new" }) : go({ to: "/self-assessment/getting-started" }) }
              }}
              variant="contained"
              color="secondary"
              sx={{
                paddingX: "24px",
                textTransform: 'uppercase',
                alignSelf: 'center',
                backgroundColor: '#DFFF32',
              }}
            >
              {currentPage ? "Continue Assessment" : "Start Assessment"}
            </MLButton>
            <MLTypography variant="body1" fontSize={"12px"} fontWeight={500} lineHeight={1.2}>
              Invest <span style={{ fontWeight: 700 }}>10 mins</span> for your ergonomic wellbeing
            </MLTypography>
          </Stack>
        </Stack>
      )}
      <AskAQuestion
        openAskAQuestionModal={isNeedHelpModalOpened}
        handleClose={() => setIsNeedHelpModalOpened(false)}
        isActionPlan={true}
      />
    </Stack>
  );
};

export default ErgoScoreActionPlan;