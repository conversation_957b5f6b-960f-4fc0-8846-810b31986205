import DaysAutocomplete from "../../../components/autocomplete/DaysAutocomplete/DaysAutocomplete";
import MLCard from "../../../components/ui/MLCard/MLCard";
import MLSingleSelect from "../../../components/ui/MLSingleSelecttag/MLSingleSelect";
import MLTypography from "../../../components/ui/MLTypography/MLTypography";
import { RequestType } from "../../../models/Case";
import User from "../../../models/User";
import CalendarView from "../../cases/create/components/CalendarView/CalendarView";
import CustomTimeslotSelect from "../../cases/create/components/ScheduleCard/CustomTimeslotSelect";
import useScheduleCard from "../../cases/create/components/ScheduleCard/useScheduleCard";
import {
    Box,
    CardContent,
    MenuItem,
    Stack,
    Tab,
    Tabs,
    Typography,
} from "@mui/material";
import Switch from "@mui/material/Switch";
import { useOne } from "@refinedev/core";
import dayjs from "dayjs";
import { PropsWithChildren, useEffect } from "react";

const FIXED_DURATION = 30; // Fixed duration in minutes

function CustomTabPanel(
    props: PropsWithChildren<{
        index: number;
        value: number;
    }>,
) {
    const { children, value, index, ...other } = props;

    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`simple-tabpanel-${index}`}
            aria-labelledby={`simple-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ paddingX: 0, paddingY: 3 }}>{children}</Box>
            )}
        </div>
    );
}

interface ScheduleCardProps extends ReturnType<typeof useScheduleCard> {
    user_id: number;
    employeeName: string;
    defaultChecked?: boolean;
    assignContentSlot?: JSX.Element;
    anyPerson?: boolean;
    casetype?: RequestType;
}

// TODO: there is a bug with the switch component's onChange prop. currently using onClick as a workaround.
export default function RequestAssessScheduleCard(props: ScheduleCardProps) {
    const {
        checked,
        setChecked,
        currentTab,
        setValue,
        handleTabChange,
        days,
        selectedDays,
        setSelectedDays,
        durationSelected,
        setDurationSelected,
        amOrPm,
        setAmorPM,
        success,
        slotsData,
        timeZoneOptions,
        selectedTimezone,
        setSelectedTimezone,
        selectedSlot,
        setSelectedSlot,
        newAppointment,
        setNewAppointment,
        defaultChecked,
        assignContentSlot,
        anyPerson,
        casetype,
    } = props;

    // console.log("casetype", casetype);

    const { data: userData, isSuccess: userIsSuccess } = useOne<User>({
        resource: "users",
        id: props.user_id,
        meta: {
            populate: {
                appointments: {
                    populate: {
                        case: {
                            populate: "employee",
                        },
                    },
                },
                working_hours: {
                    populate: "*",
                },
            },
        },
    });

    useEffect(() => {
        if (selectedSlot) {
            setNewAppointment({
                start: selectedSlot,
                end: dayjs(selectedSlot).add(FIXED_DURATION, "minutes").toDate(),
                title: props.employeeName,
                editable: true,
                durationEditable: false,
                startEditable: true,
            });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedSlot]);

    return (
        <MLCard>
            <CardContent>
                <Stack direction="column" gap={2}>
                    <Stack direction="row" alignItems={"center"}>
                        <MLTypography variant="h2">Schedule Assessment</MLTypography>
                        {defaultChecked ? null : (
                            <Switch
                                disabled={false}
                                checked={checked}
                                onClick={() => setChecked(!checked)}
                                inputProps={{ "aria-label": "controlled" }}
                            />
                        )}
                    </Stack>
                    {assignContentSlot}
                    {checked || defaultChecked ? (
                        <Stack direction="column" gap={0}>
                            <Stack direction="column" gap={1}>
                                <Typography
                                    sx={{
                                        fontSize: "16px",
                                        fontWeight: 500,
                                        color: "#000000",
                                        mb: 1,
                                    }}
                                >
                                    Duration : {FIXED_DURATION} mins
                                </Typography>
                            </Stack>

                            {/*<Stack direction="column" gap={1} pt={1}>
                <MLTypography fontWeight={"medium"}>Timezone</MLTypography>
                 <Autocomplete
                  value={selectedTimezone ?? null}
                  onChange={(event, newValue: string | null) =>
                    setSelectedTimezone(newValue ?? "")
                  }
                  options={timeZoneOptions.map((e) => e.title)}
                  defaultValue={""}
                  renderInput={(params) => (
                    <MLTextField
                      {...params}
                      variant="filled"
                      label="Timezone"
                      placeholder="Timezone"
                    />
                  )}
                ></Autocomplete> 
              </Stack> */}
                            <Stack>
                                {/* <Box
                                    display={{ md: "block", xs: "none" }}
                                    sx={{ borderBottom: 1, borderColor: "divider" }}
                                >
                                    <Tabs
                                        value={currentTab}
                                        onChange={handleTabChange}
                                        aria-label="basic tabs example"
                                    >
                                        <Tab label="Quick dates" />
                                        <Tab label="Calendar view" />
                                    </Tabs>
                                </Box> */}
                                <CustomTabPanel value={currentTab} index={0}>
                                    <Stack direction={"column"} gap={1}>
                                        <Stack
                                            direction="row"
                                            gap="30px"
                                            alignItems="center"
                                            justifyContent="space-between"
                                            flexWrap="wrap"
                                        >
                                            <Stack
                                                direction={{ xs: "column", sm: "row" }}
                                                gap="10px"
                                                alignItems={{ xs: "flex-start", sm: "center" }}
                                                flexGrow={1}
                                            >
                                                <Typography
                                                    sx={{
                                                        fontSize: "16px",
                                                        fontWeight: 600,
                                                        lineHeight: "120%",
                                                        minWidth: "155px", // Fixed width for label
                                                        flexShrink: 0, // Prevent label from shrinking
                                                    }}
                                                >
                                                    Preferred days
                                                </Typography>

                                                <DaysAutocomplete
                                                    days={days}
                                                    selectedDays={selectedDays}
                                                    setSelectedDays={setSelectedDays}
                                                    sx={{
                                                        width: {
                                                            xs: "100%", // extra-small devices and up
                                                            lg: "70%", // large devices and up
                                                        },
                                                    }}
                                                />
                                            </Stack>
                                        </Stack>

                                        <Stack
                                            direction="row"
                                            gap="10px"
                                            alignItems="center"
                                            flexGrow={1}
                                            mt={1}
                                        >
                                            <Typography
                                                sx={{
                                                    fontSize: "16px",
                                                    fontWeight: 600,
                                                    lineHeight: "120%",
                                                    minWidth: "155px", // Same fixed width as above
                                                    flexShrink: 0, // Prevent label from shrinking
                                                }}
                                            >
                                                Preferred timings
                                            </Typography>
                                            <MLSingleSelect
                                                heading=""
                                                value={amOrPm}
                                                fullWidth
                                                onChange={(event: any) => setAmorPM(event.target.value)}
                                            >
                                                {[
                                                    { value: "Any", id: 1 },
                                                    { value: "AM", id: 2 },
                                                    { value: "PM", id: 2 },
                                                ].map((item, index) => (
                                                    <MenuItem value={item.value} key={item.value}>
                                                        {item.value}
                                                    </MenuItem>
                                                ))}
                                            </MLSingleSelect>
                                        </Stack>

                                        <CustomTimeslotSelect
                                            slotsData={slotsData}
                                            timezone={selectedTimezone}
                                            selectedSlot={selectedSlot}
                                            setSelectedSlot={setSelectedSlot}
                                            changeCalendarView={() => setValue(1)}
                                        />
                                    </Stack>
                                </CustomTabPanel>
                                <CustomTabPanel value={currentTab} index={1}>
                                    <Stack ml={2}>
                                        <Typography
                                            variant="body1"
                                            fontSize={16}
                                            color={"#000"}
                                            fontWeight={500}
                                            marginTop={-1}
                                            marginBottom={1.4}
                                        >
                                            Select available dates
                                        </Typography>

                                        {userData?.data.appointments &&
                                            userData?.data.working_hours &&
                                            !anyPerson ? (
                                            <CalendarView
                                                appointments={userData.data.appointments ?? []}
                                                workingHours={userData.data.working_hours ?? []}
                                                // durationInMinutes={durationSelected}
                                                durationInMinutes={FIXED_DURATION}
                                                employeeName={props.employeeName}
                                                casetype={casetype} // Pass casetype to CalendarView
                                                newAppointment={newAppointment}
                                                setNewAppointment={setNewAppointment}
                                            />
                                        ) : (
                                            <MLTypography>
                                                Calendar view is not available when viewing timeslots of
                                                multiple ergonomists.
                                            </MLTypography>
                                        )}
                                    </Stack>
                                </CustomTabPanel>
                            </Stack>
                        </Stack>
                    ) : null}
                </Stack>
            </CardContent>
        </MLCard>
    );
}
