import React, { useState, useEffect, useContext, useRef } from "react";
import { Stack, Grid, Dialog, Box, CardContent, CircularProgress, Alert, IconButton } from "@mui/material";
import { useCreate, useCreateMany, useCustomMutation, useGo, useBack, useUpdate, useList, useOne } from "@refinedev/core";
import { API_URL } from "../../../constants";
import MLBanner from "../../../components/ui/MLBanner/MLBanner";
import MLCard from "../../../components/ui/MLCard/MLCard";
import MLButton from "../../../components/ui/MLButton/MLButton";
import MLTypography from "../../../components/ui/MLTypography/MLTypography";
import MLToggleButtonGroup from "../../../components/ui/MLToggleButtonGroup/MLToggleButtonGroup";
import MLToggleButton from "../../../components/ui/MLToggleButton/MLToggleButton";
import Case, { CaseMode, RequestType, CaseStatus, requestTypeLabels } from "../../../models/Case";
import useRequestParticularForm from "./useRequestParticularForm";
import { EmailFetchedState } from "./useRequestParticularForm";
import RequestParticularForm from "./RequestParticularForm";
import RequestAssessScheduleCard from "./RequestAssessScheduleCard";
import useAssignCard from "../../cases/create/components/AssignCard/useAssignCard";
import useScheduleCard from "./useRequestSceduleCard";
import useOtherDetails from "../../cases/create/components/OtherDetailsCard/useOtherDetails";
import { useErrorDialog } from "../../../contexts/error-dialog/error-dialog";
import { MLMobileButton, MLMobileButtonGroup } from "../../../components/ui/MLMobileButtonGroup/MLMobileButtonGroup";
import { formatEnumString } from "../../../utils/enumUtils";
import MLMultiSelecttag from "../../../components/ui/MLMultiSelecttag/MLSelecttag";
import OtherDetailsCard from "../../cases/create/components/OtherDetailsCard/OtherDetailsCard";
import AssignCard from "./AssignCard";
import { desktop, tablet } from "../../../responsiveStyles";
import MLContainer from "../../../components/ui/MLMaxWidthContainer/MLMaxWidthContainer";
import { StatusMessage } from "../../MyAssessments/myAssessmentUtils";
import useCaseRequestFormSettings from "../../cases/create/useCaseRequestFormSettings";
import { AuthContext } from "../../../contexts/authContext/AuthContext";
import { ViewModeContext } from "../../../contexts/ViewModeContext/ViewModeContext";
import { ErChevronleft } from "@mindlens/ergo-icons";
import { useParams } from "react-router-dom";
import { countries } from "../../../components/autocomplete/CountryCodeAutocomplete/countries";
import { capitalizeWords } from "../../../utils/colorCodeUtils";
import Employee from "../../../models/Employee";

type OptionType = {
    label: string;
    value: number;
};

interface RsiBodyPartConfig {
    id: number;
    bodyPartText: string;
}

export const RequestAssessment: React.FC = () => {
    const { userDetails: loggedUserDetails } = useContext(AuthContext);
    const { isEmployeeView } = useContext(ViewModeContext);

    // Get employeeId from URL params
    const { employeeId } = useParams<{ employeeId: string }>();

    const loggedUserRoleIsEmployee = loggedUserDetails?.role?.name.toLowerCase() === "employee" || loggedUserDetails?.employeeView || isEmployeeView;

    // Important: Only use autofetch if we don't have an employeeId in the URL
    // This prevents the current user's data from loading when we have a specific employee in the URL
    const shouldAutofetch = !employeeId;

    // Fetch employee data if employeeId is provided - use this for the specific employee case
    const { data: employeeData, isLoading: isEmployeeLoading } = useOne<Employee>({
        resource: "employees",
        id: employeeId,
        queryOptions: {
            enabled: !!employeeId, // Only run query if employeeId exists
        },
        meta: {
            populate: {
                organization: {
                    populate: ["addresses"]
                },
                address: true
            }
        }
    });

    // Mutations and their loading/error states
    const { mutate: mutateCreateCase, isLoading: isCreateCaseLoading, error: createCaseError } = useCreate();
    const { mutate: mutateCreateEmployee, isLoading: isCreateEmployeeLoading, error: createEmployeeError } = useCreate();
    const { mutate: mutateCreateAppointment, isLoading: isCreateAppointmentLoading, error: createAppointmentError } = useCreate();
    const { mutate: mutateCreateMany, isLoading: isCreateManyLoading, error: createManyError } = useCreateMany();
    const { mutate: mutateUpdateEmployee, isLoading: isUpdateEmployeeLoading, error: updateEmployeeError } = useUpdate();
    const { mutate: mutateCustom, isLoading: isCustomLoading, error: customError } = useCustomMutation();

    const go = useGo();
    const back = useBack();

    // State management
    const [caseModeSelected, setCaseModeSelected] = useState(CaseMode.IN_PERSON);
    const [requestTypeSelected, setRequestTypeSelected] = useState(RequestType.GENERAL_ASSESSMENT);
    const [open, setOpen] = useState(false);
    const [tempUserId, setTempUserId] = useState<number>();
    const [bodyParts, setBodyParts] = useState<OptionType[]>([]);
    const [selectedBodyParts, setSelectedBodyParts] = useState<OptionType[]>([]);
    const [caseDetails, setCaseDetails] = useState<Partial<Case>>();
    const [errorMessage, setErrorMessage] = useState<string | null>(null);
    const [successMessage, setSuccessMessage] = useState<string | null>(null);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isFormPrefilled, setIsFormPrefilled] = useState(false);
    const [isUsingUrlEmployeeData, setIsUsingUrlEmployeeData] = useState(false);

    // Get form settings for dynamic fields (not pre-login)
    const {
        formSettings,
        isLoading: isFormSettingsLoading,
        enabledRequestTypes,
        enabledModeOfAssessments,
        enabledOptionalFields,
        hasEnabledOptions,
    } = useCaseRequestFormSettings(false);

    // Extract mandatory fields with type safety
    const mandatoryFields = formSettings && formSettings.mandatoryInformation
        ? (Object.keys(formSettings.mandatoryInformation) as Array<keyof typeof formSettings.mandatoryInformation>)
            .filter(key => formSettings.mandatoryInformation[key])
        : ['name', 'email'] as string[];

    // 1. First, create a ref to track if we've already applied the autofetch disable
    const hasDisabledAutofetchRef = useRef(false);

    // Custom hooks - Pass shouldAutofetch to ensure we don't load current user data when we have an employeeId
    const particularsProps = useRequestParticularForm(caseModeSelected, shouldAutofetch, isEmployeeLoading);
    const assignCardProps = useAssignCard(false);
    const scheduleCardProps = useScheduleCard(
        assignCardProps.selectedUserId,
        assignCardProps.anyPerson,
        setTempUserId
    );
    const otherDetailsProps = useOtherDetails();
    const { showError } = useErrorDialog();

    // Fetch body part configurations
    const { data: bodyPartConfigsData, isLoading: isBodyPartsLoading, error: bodyPartsError } = useList<RsiBodyPartConfig>({
        resource: "rsi-body-part-configs",
        pagination: { pageSize: 30 },
    });

    // Combined loading state
    const isLoading = isCreateCaseLoading || isCreateEmployeeLoading || isCreateAppointmentLoading ||
        isCreateManyLoading || isUpdateEmployeeLoading || isCustomLoading ||
        isBodyPartsLoading || isFormSettingsLoading ||
        (employeeId ? isEmployeeLoading : particularsProps.isCurrentEmployeeLoading);

    // Combined error state
    const hasError = createCaseError || createEmployeeError || createAppointmentError ||
        createManyError || updateEmployeeError || customError ||
        bodyPartsError || errorMessage ||
        (employeeId ? false : particularsProps.isUserError);

    // Extract relevant props from particularsProps
    const {
        fullName,
        email,
        emailError,
        countryCode,
        contactNo,
        contactNoError,
        companyAddressSelected,
        employeeLevel,
        currentEmployee,
        employeeDeskNo,
        emailFetched,
        allowEdits,
        companySelected,
        handleGeneralEditStart,
        emailOrgAccessError,
        domainError,
        organizations,
    } = particularsProps;

    // Filter request types based on enabled settings
    const getEnabledRequestTypes = () => {
        if (!formSettings || !enabledRequestTypes || enabledRequestTypes.length === 0) {
            return Object.values(RequestType); // Show all if no settings
        }

        return Object.values(RequestType).filter(type => {
            switch (type) {
                case RequestType.ACCOMODATION: return enabledRequestTypes.includes('accomodation');
                case RequestType.BODY_DISCOMFORT: return enabledRequestTypes.includes('bodyDiscomfort');
                case RequestType.GENERAL_ASSESSMENT: return enabledRequestTypes.includes('generalAssessment');
                case RequestType.PRODUCT_REQUISITE: return enabledRequestTypes.includes('productRequisite');
                case RequestType.NEW_OFFICE_ASSESSMNT: return enabledRequestTypes.includes('newOfficeAssessment');
                default: return false;
            }
        });
    };

    // Filter case modes based on enabled settings
    const getEnabledCaseModes = () => {
        if (!formSettings || !enabledModeOfAssessments || enabledModeOfAssessments.length === 0) {
            return Object.values(CaseMode); // Show all if no settings
        }

        return Object.values(CaseMode).filter(mode => {
            switch (mode) {
                case CaseMode.REMOTE: return enabledModeOfAssessments.includes('remote');
                case CaseMode.IN_PERSON: return enabledModeOfAssessments.includes('inPerson');
                default: return false;
            }
        });
    };

    // Get enabled request types and modes
    const enabledRequestTypesList = getEnabledRequestTypes();
    const enabledCaseModesList = getEnabledCaseModes();

    // Set default values based on what's enabled
    useEffect(() => {
        if (enabledRequestTypesList.length > 0 && !enabledRequestTypesList.includes(requestTypeSelected)) {
            setRequestTypeSelected(enabledRequestTypesList[0]);
        }

        if (enabledCaseModesList.length > 0 && !enabledCaseModesList.includes(caseModeSelected)) {
            setCaseModeSelected(enabledCaseModesList[0]);
        }
    }, [enabledRequestTypesList, enabledCaseModesList]);

    // IMPORTANT: This effect ensures we're using the URL employee data and disables currentEmployee loading
    useEffect(() => {
        if (employeeId && !hasDisabledAutofetchRef.current) {
            setIsUsingUrlEmployeeData(true);

            // Only disable autofetch once to prevent re-renders
            if (particularsProps.setAutofetchDisabled) {
                particularsProps.setAutofetchDisabled(true);
                hasDisabledAutofetchRef.current = true;
            }

            // Only reset form prefilled state if needed
            if (isFormPrefilled) {
                setIsFormPrefilled(false);
            }
        } else if (!employeeId) {
            setIsUsingUrlEmployeeData(false);
            hasDisabledAutofetchRef.current = false;
        }
    }, [employeeId]);

    // Pre-fill form with employee data when employeeData is fetched for admin route
    useEffect(() => {
        // Only run this effect once when data is loaded and form is not yet prefilled
        if (employeeId &&
            employeeData?.data &&
            !isEmployeeLoading &&
            organizations &&
            !isFormPrefilled &&
            hasDisabledAutofetchRef.current) {

            // Use a batch update approach to prevent multiple re-renders
            const employee = employeeData.data;

            // Pre-fill all fields at once to reduce state updates
            if (particularsProps.setFullName && employee.name) {
                particularsProps.setFullName(employee.name);
            }

            if (particularsProps.setEmail && employee.email) {
                particularsProps.setEmail(employee.email);
                particularsProps.setEmailError(false);
                particularsProps.setEmailFetched(EmailFetchedState.FOUND);
            }

            if (employee.contact) {
                particularsProps.setContactNo(parseInt(employee.contact.toString()));
                particularsProps.setContactNoError(false);
            }

            if (employee.countryCode) {
                const countryMatch = countries.find(c => parseInt(c.phone) === employee.countryCode);
                if (countryMatch) {
                    particularsProps.setCountryCode(countryMatch);
                }
            }

            if (employee.level) {
                particularsProps.setEmployeeLevel(employee.level);
            }

            if (employee.deskNo) {
                particularsProps.setEmployeeDeskno(employee.deskNo);
            }

            // Set organization and address
            if (employee.organization && organizations) {
                const orgMatch = organizations.find((org: any) => org?.id === employee.organization.id);
                if (orgMatch) {
                    particularsProps.setCompanySelectedIDAutocomplete({
                        value: orgMatch.id,
                        label: orgMatch.name
                    });

                    if (employee.address && orgMatch.addresses) {
                        const addressMatch = orgMatch.addresses.find((addr: any) => addr.id === employee.address.id);
                        if (addressMatch) {
                            particularsProps.setCompanyAddressSelected({
                                value: addressMatch.id,
                                label: addressMatch.address
                            });
                        }
                    }
                }
            }

            // Enable editing for fields that might need adjustment
            particularsProps.handleGeneralEditStart();

            // Mark form as prefilled to prevent duplicated effect - do this last
            setIsFormPrefilled(true);
        }
    }, [employeeId, employeeData, isEmployeeLoading, organizations, isFormPrefilled]);

    useEffect(() => {
        if (successMessage) {
            const timer = setTimeout(() => {
                setSuccessMessage(null);
            }, 3000);
            return () => clearTimeout(timer);
        }
    }, [successMessage]);

    // Handle image creation for case
    function handleImageCreate(case_id: number) {
        if (otherDetailsProps.fileList.length === 0) {
            setOpen(true);
            setIsSubmitting(false);
            return;
        }

        const formData = new FormData();
        otherDetailsProps.fileList.forEach(file => {
            formData.append("files", file);
        });
        formData.append("refId", JSON.stringify(case_id));
        formData.append("ref", "api::case.case");
        formData.append("field", "mediaAttachments");

        mutateCustom(
            {
                url: `${API_URL}/api/upload`,
                method: "post",
                values: formData,
            },
            {
                onSuccess: () => {
                    setOpen(true);
                    setIsSubmitting(false);
                    setSuccessMessage("Assessment request created successfully!");
                },
                onError: (error) => {
                    setIsSubmitting(false);
                    console.error("Image upload error:", error);
                    setErrorMessage("Image upload failed. Please try again or continue without images.");
                    showError(
                        "Image upload failed. Please go to the case edit page and try submitting again. Make sure that the photos are of either jpg or png format.",
                        () => go({ to: `/cases/edit/${case_id}` }),
                        "Edit Case"
                    );
                },
            }
        );
    }

    function handleSubmit(e: React.FormEvent) {
        e.preventDefault();
        setErrorMessage(null);
        setIsSubmitting(true);

        // Basic validation checks
        if (contactNoError) {
            setErrorMessage("Please correct the contact number errors before submitting.");
            setIsSubmitting(false);
            return;
        }

        if (emailError || emailOrgAccessError || domainError) {
            setErrorMessage("Please correct the email errors before submitting.");
            setIsSubmitting(false);
            return;
        }

        // Check if required fields are filled based on form settings
        const isValid = mandatoryFields.every(field => {
            switch (field) {
                case 'name': return !!fullName;
                case 'email': return !emailError && !!email;
                case 'contactNo': return !contactNoError && !!contactNo;
                default: return true;
            }
        });

        if (!isValid) {
            setErrorMessage("Please fill in all required fields correctly.");
            setIsSubmitting(false);
            return;
        }

        if (!companySelected) {
            setErrorMessage("Please select a company.");
            setIsSubmitting(false);
            return;
        }

        if (enabledOptionalFields.includes('companyAddress') && !companyAddressSelected?.value) {
            setErrorMessage("Please select a company address.");
            setIsSubmitting(false);
            return;
        }

        // CRITICAL: For admin route, prioritize employeeId from URL
        // For regular route, use currentEmployee id if available
        const employeeIdForSubmission = isUsingUrlEmployeeData && employeeId
            ? Number(employeeId)
            : currentEmployee?.data?.employee?.id;

        // Create employee object with only relevant fields based on enabled settings
        const employeeBase: Record<string, any> = {
            id: employeeIdForSubmission,
        };

        // Always include name and email (standard fields)
        if (mandatoryFields.includes('name') || enabledOptionalFields.includes('name')) {
            employeeBase.name = fullName;
        }

        if (mandatoryFields.includes('email') || enabledOptionalFields.includes('email')) {
            employeeBase.email = email;
        }

        // Include contact details only if enabled
        if (mandatoryFields.includes('contactNo') || enabledOptionalFields.includes('contactNo')) {
            employeeBase.contact = contactNo ?? null;
            employeeBase.countryCode = parseInt(countryCode.phone);
        }

        // Include level and desk number only if in-person mode AND fields are enabled
        if (caseModeSelected === CaseMode.IN_PERSON) {
            if (mandatoryFields.includes('level') || enabledOptionalFields.includes('level')) {
                employeeBase.level = employeeLevel || null;
            }

            if (mandatoryFields.includes('deskNo') || enabledOptionalFields.includes('deskNo')) {
                employeeBase.deskNo = employeeDeskNo || null;
            }
        }

        // Include organization details
        if (companySelected) {
            employeeBase.organization = companySelected;

            if (mandatoryFields.includes('companyAddress') || enabledOptionalFields.includes('companyAddress')) {
                employeeBase.address = companySelected?.addresses.find(
                    (e: { id: number; address: string }) => e.id === companyAddressSelected?.value,
                ) ?? null;
            }
        }

        // Handle existing employee update
        if (employeeBase.id) {
            mutateUpdateEmployee(
                {
                    resource: "employees",
                    values: employeeBase,
                    id: employeeBase.id,
                },
                {
                    onSuccess: (response) => {
                        createCase(employeeBase.id!);
                    },
                    onError: (error: any) => {
                        setIsSubmitting(false);
                        console.error("Employee update error:", error);
                        // Check if it's a unique email error
                        if (error?.error?.details?.errors?.some(
                            (e: any) => e.path?.includes('email') && e.message === 'This attribute must be unique'
                        )) {
                            setErrorMessage("This email is already associated with another employee. Please use a different email.");
                            showError(
                                "This email is already associated with another employee. Please use a different email.",
                                undefined,
                                "Email Error"
                            );
                        } else {
                            setErrorMessage("Failed to update employee details. Please try again.");
                            showError("Failed to update employee details. Please try again.");
                        }
                    },
                },
            );
        } else {
            // For new employee creation, prepare the address properly if it exists
            if (employeeBase.address) {
                employeeBase.address = {
                    id: employeeBase.address.id,
                };
            }

            // Handle new employee creation
            mutateCreateEmployee(
                {
                    resource: "employees",
                    values: employeeBase,
                },
                {
                    onSuccess: (data) => {
                        createCase(data.data.data.id as number);
                    },
                    onError: (error: any) => {
                        setIsSubmitting(false);
                        console.error("Employee creation error:", error);
                        // Check if it's a unique email error
                        if (error?.error?.details?.errors?.some(
                            (e: any) => e.path?.includes('email') && e.message === 'This attribute must be unique'
                        )) {
                            setErrorMessage("This email is already associated with another employee. Please use a different email.");
                            showError(
                                "This email is already associated with another employee. Please use a different email.",
                                undefined,
                                "Email Error"
                            );
                        } else {
                            setErrorMessage("Failed to create employee record. Please try again.");
                            showError("Failed to create employee record. Please try again.");
                        }
                    },
                },
            );
        }
    }

    // Helper function to create a case
    function createCase(employeeID: number) {
        mutateCreateMany(
            {
                resource: "rsi-body-parts",
                values: bodyParts.map((bodyPart) => ({
                    checked: true,
                    rsiBodyPartConfig: bodyPart.value,
                })),
            },
            {
                onSuccess: (data) => {
                    mutateCreateCase(
                        {
                            resource: "cases",
                            values: {
                                mode: caseModeSelected,
                                type: requestTypeSelected,
                                employee: employeeID,
                                assessmentAssignedTo: assignCardProps.checked && assignCardProps.selectedUserId !== undefined
                                    ? assignCardProps.selectedUserId
                                    : tempUserId
                                        ? tempUserId.toString()
                                        : null,
                                rsi_body_parts: data.data.map((e) => e.data.id),
                                status: scheduleCardProps.checked && scheduleCardProps.newAppointment
                                    ? CaseStatus.SCHEDULED
                                    : (assignCardProps.checked && assignCardProps.selectedUserId) || tempUserId
                                        ? CaseStatus.ASSIGNED
                                        : CaseStatus.CREATED,
                                notes: otherDetailsProps.notes,
                            },
                        },
                        {
                            onSuccess: (d) => {
                                setCaseDetails({
                                    id: d.data.data.id,
                                });

                                if (scheduleCardProps.checked && scheduleCardProps.newAppointment) {
                                    createAppointment(d.data.data.id);
                                } else {
                                    handleImageCreate(d.data.data.id);
                                }
                            },
                            onError: (error) => {
                                setIsSubmitting(false);
                                console.error("Case creation error:", error);
                                setErrorMessage("Failed to create case. Please try again.");
                                showError("Failed to create case. Please try again.");
                            },
                        },
                    );
                },
                onError: (error) => {
                    setIsSubmitting(false);
                    console.error("Body parts creation error:", error);
                    setErrorMessage("Failed to create body part records. Please try again.");
                    showError("Failed to create body part records. Please try again.");
                }
            },
        );
    }

    // Helper function to create appointment
    function createAppointment(caseId: number) {
        // Validate appointment data
        if (!scheduleCardProps.newAppointment?.start || !scheduleCardProps.newAppointment?.end) {
            setErrorMessage("Invalid appointment time. Please select a valid time slot.");
            setIsSubmitting(false);
            return;
        }

        mutateCreateAppointment(
            {
                resource: "appointments",
                values: {
                    startTime: scheduleCardProps.newAppointment?.start,
                    endTime: scheduleCardProps.newAppointment?.end,
                    case: { id: caseId },
                    employees: currentEmployee?.data?.employee?.id
                },
            },
            {
                onSuccess: () => handleImageCreate(caseId),
                onError: (error) => {
                    setIsSubmitting(false);
                    console.error("Appointment creation error:", error);
                    setErrorMessage("Failed to schedule appointment. Please try again.");
                    showError("Failed to schedule appointment. Please try again.");
                },
            },
        );
    }

    const handleSelectedExistingBodyDiscomfort = (selectedValues: OptionType[]) => {
        setBodyParts(selectedValues);
        setSelectedBodyParts(selectedValues);
    };

    // Sort body part options
    const sortedBodyPartOptions = bodyPartConfigsData?.data
        .map((config) => ({
            label: config.bodyPartText,
            value: config.id,
        }))
        .sort((a, b) => a.label.toLowerCase().localeCompare(b.label.toLowerCase())) ?? [];

    // Validation for request button
    const isRequestButtonDisabled = (): boolean => {
        // Check mandatory fields based on form settings
        const requiredFieldsMissing = mandatoryFields.some(field => {
            switch (field) {
                case 'name': return !fullName;
                case 'email': return !email;
                case 'contactNo': return !contactNo;
                case 'companyAddress': return !companyAddressSelected?.value;
                default: return false;
            }
        });

        if (formSettings.optional.contactNo && (!contactNo || contactNoError)) {
            return true;
        }

        const hasEmailErrors = Boolean(emailError || emailOrgAccessError || domainError);
        const hasContactErrors = Boolean(contactNoError);
        const companyMissing = !companySelected;

        return Boolean(
            requiredFieldsMissing ||
            hasEmailErrors ||
            hasContactErrors ||
            companyMissing ||
            isSubmitting
        );
    };

    // Handle the main page loading state
    if (isLoading) {
        return (
            <Box height={650} display="flex" justifyContent="center" alignItems="center">
                <Stack alignItems="center" spacing={2}>
                    <CircularProgress color="primary" />
                    <MLTypography>Loading assessment form...</MLTypography>
                </Stack>
            </Box>
        );
    }

    // Handle errors from data fetching
    if ((employeeId ? false : particularsProps.orgIsError) || (employeeId ? false : particularsProps.isUserError) || bodyPartsError) {
        return (
            <Box height={650} display="flex" justifyContent="center" alignItems="center">
                <MLContainer>
                    <StatusMessage
                        title="Unable to Load Assessment Form"
                        message="We're having trouble loading the assessment request form. Please try again later or contact support."
                        type="error"
                    />
                </MLContainer>
            </Box>
        );
    }

    const hasNoValidFormConfig = !isFormSettingsLoading &&
        !hasEnabledOptions('requestType') &&
        !hasEnabledOptions('modeOfAssessment');

    if (hasNoValidFormConfig) {
        return (
            <Box
                height={"650px"}
                display={"flex"}
                justifyContent={"center"}
                alignItems={"center"}
            >
                <Stack spacing={2} alignItems="center" maxWidth={600}>
                    <MLTypography variant="h2">Form Configuration Required</MLTypography>
                    <Alert severity="warning" sx={{ width: '100%' }}>
                        The case request form has not been properly configured. <br />
                        Please contact your administrator to set up the required fields in Settings.
                    </Alert>
                    <MLButton
                        variant="contained"
                        color="secondary"
                        onClick={() => back()}
                    >
                        Go Back
                    </MLButton>
                </Stack>
            </Box>
        );
    }

    return (
        <>
            {
                loggedUserRoleIsEmployee && <MLBanner
                    backgroundColor='#E3DDFF'
                    title="Request Ergo Assessment"
                    subtitle='Track and manage your ergonomic support requests'
                />
            }
            <form onSubmit={handleSubmit}>
                <Stack
                    direction="column"
                    height="100%"
                    sx={{
                        backgroundImage: "url('/background_waves/horizontal1.svg')",
                        backgroundSize: "cover",
                        backgroundAttachment: "fixed",
                        // paddingY: {
                        //     md: tablet.contentContainer.paddingY,
                        //     xs: tablet.contentContainer.paddingY,
                        // },
                       
                    }}
                >
                    {
                        !loggedUserRoleIsEmployee && (
                            <Stack
                                direction="row"
                                alignItems="center"
                                spacing={1}
                                mb={3}
                                mx={2}
                            >
                                <IconButton size="large" onClick={() => back()}>
                                    <ErChevronleft />
                                </IconButton>
                                <MLTypography
                                    variant="h1"
                                    fontWeight={700}
                                    sx={{
                                        fontSize: { xs: '1rem', sm: '1.5rem', md: '40px' },
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        whiteSpace: { xs: 'normal', sm: 'nowrap' },
                                        lineHeight: { xs: 1.2, sm: 1.5 }
                                    }}
                                >
                                    {employeeId && employeeData?.data ?
                                        "Request Assessment for : " + capitalizeWords(employeeData.data.name) :
                                        "Request an Assessment"}
                                </MLTypography>
                            </Stack>
                        )
                    }
                    <Dialog
                        open={open}
                        onClose={() => setOpen(false)}
                        fullWidth
                        maxWidth="lg"
                    >
                        <Stack
                            direction="column"
                            alignItems="center"
                            gap={2}
                            py={{ xs: 3, sm: 6 }}
                            px={{ xs: 4, sm: 8 }}
                        >
                            <MLTypography variant="h3" sx={{ textAlign: "center" }}>
                                Assessment Request Submitted
                            </MLTypography>
                            <Stack>
                                <MLTypography>
                                    Request ID: {caseDetails?.id ?? "Unknown"}
                                </MLTypography>
                                <MLTypography>
                                    Our team will review your request and assign an ergonomist shortly.
                                </MLTypography>
                            </Stack>
                            <MLButton
                                variant="contained"
                                color="secondary"
                                onClick={() => go({ to: "/" })}
                            >
                                OK
                            </MLButton>
                        </Stack>
                    </Dialog>
                    <MLContainer>
                        {errorMessage && (
                            <Box mb={2}>
                                <StatusMessage
                                    title="Error while submitting form"
                                    message={errorMessage}
                                    type="error"
                                />
                            </Box>
                        )}

                        {successMessage && (
                            <Box mb={2}>
                                <StatusMessage
                                    title="Request Submitted Successfully"
                                    message={successMessage}
                                    type="info"
                                />
                            </Box>
                        )}

                        <Grid container spacing={2}>
                            <Grid item xs={12}>
                                <MLCard>
                                    <CardContent>
                                        <Stack gap={2}>
                                            <MLTypography variant="h2">Request details</MLTypography>
                                            <Stack gap={3}>
                                                <Stack gap={1}>
                                                    <MLTypography variant="body1" fontWeight="medium">
                                                        Preferred mode of assessment
                                                    </MLTypography>
                                                    <Stack display={{ md: "none", xs: "block" }}>
                                                        <MLMobileButtonGroup
                                                            value={caseModeSelected}
                                                            onChange={(newValue: string | null) =>
                                                                setCaseModeSelected(newValue as CaseMode)
                                                            }
                                                            exclusive
                                                        >
                                                            {enabledCaseModesList.map((mode) => (
                                                                <MLMobileButton
                                                                    key={mode}
                                                                    value={mode}
                                                                    label={formatEnumString(mode)}
                                                                />
                                                            ))}
                                                        </MLMobileButtonGroup>
                                                    </Stack>
                                                    <Stack display={{ md: "block", xs: "none" }}>
                                                        <MLToggleButtonGroup
                                                            color="primary"
                                                            exclusive
                                                            size="small"
                                                            value={caseModeSelected}
                                                            onChange={(event, newValue: string | null) =>
                                                                setCaseModeSelected(newValue as CaseMode)
                                                            }
                                                        >
                                                            {enabledCaseModesList.map((mode) => (
                                                                <MLToggleButton
                                                                    key={mode}
                                                                    value={mode}
                                                                    sx={enabledCaseModesList.length === 1 ?
                                                                        { "&.MuiToggleButton-root:first-of-type:last-child": { borderRadius: "8px 8px 8px 8px !important" } } :
                                                                        {}
                                                                    }
                                                                >
                                                                    {formatEnumString(mode)}
                                                                </MLToggleButton>
                                                            ))}
                                                        </MLToggleButtonGroup>
                                                    </Stack>
                                                </Stack>

                                                <Stack gap={1}>
                                                    <MLTypography variant="body1" fontWeight="medium">
                                                        Primary reason for assessment
                                                    </MLTypography>
                                                    <Stack display={{ md: "none", xs: "block" }}>
                                                        {/* <MLMobileButtonGroup
                                                            value={requestTypeSelected}
                                                            onChange={(newValue: string | null) =>
                                                                setRequestTypeSelected(newValue as RequestType)
                                                            }
                                                            exclusive
                                                        >
                                                            {enabledRequestTypesList.map((type) => (
                                                                <MLMobileButton
                                                                    key={type}
                                                                    value={type}
                                                                    label={formatEnumString(type)}
                                                                />
                                                            ))}
                                                        </MLMobileButtonGroup> */}

                                                        <MLMobileButtonGroup
                                                            value={requestTypeSelected}
                                                            onChange={(newValue: string | null) =>
                                                                setRequestTypeSelected(newValue as RequestType)
                                                            }
                                                            exclusive
                                                        >
                                                            {enabledRequestTypesList.map((type) => (
                                                                <MLMobileButton
                                                                    key={type}
                                                                    value={type}
                                                                    label={requestTypeLabels[type]}  // Changed from formatEnumString(type)
                                                                />
                                                            ))}
                                                        </MLMobileButtonGroup>
                                                    </Stack>
                                                    <Stack display={{ md: "block", xs: "none" }}>
                                                        {/* <MLToggleButtonGroup
                                                            color="primary"
                                                            exclusive
                                                            size="small"
                                                            value={requestTypeSelected}
                                                            onChange={(event, newValue: string | null) =>
                                                                setRequestTypeSelected(newValue as RequestType)
                                                            }
                                                        >
                                                            {enabledRequestTypesList.map((type) => (
                                                                <MLToggleButton
                                                                    key={type}
                                                                    value={type}
                                                                    sx={enabledRequestTypesList.length === 1 ?
                                                                        { "&.MuiToggleButton-root:first-of-type:last-child": { borderRadius: "8px 8px 8px 8px !important" } } :
                                                                        {}
                                                                    }
                                                                >
                                                                    {formatEnumString(type)}
                                                                </MLToggleButton>
                                                            ))}
                                                        </MLToggleButtonGroup> */}


                                                        <MLToggleButtonGroup
                                                            color="primary"
                                                            exclusive
                                                            size="small"
                                                            value={requestTypeSelected}
                                                            onChange={(event, newValue: string | null) =>
                                                                setRequestTypeSelected(newValue as RequestType)
                                                            }
                                                        >
                                                            {enabledRequestTypesList.map((type) => (
                                                                <MLToggleButton
                                                                    key={type}
                                                                    value={type}
                                                                    sx={enabledRequestTypesList.length === 1 ?
                                                                        { "&.MuiToggleButton-root:first-of-type:last-child": { borderRadius: "8px 8px 8px 8px !important" } } :
                                                                        {}
                                                                    }
                                                                >
                                                                    {requestTypeLabels[type]}
                                                                </MLToggleButton>
                                                            ))}
                                                        </MLToggleButtonGroup>
                                                    </Stack>
                                                </Stack>

                                                <Box sx={{
                                                    width: { sm: "500px", xs: "auto" }
                                                }}>
                                                    <MLMultiSelecttag
                                                        value={selectedBodyParts}
                                                        heading={`Existing body discomfort`}
                                                        placeholder="Select body discomfort"
                                                        options={sortedBodyPartOptions}
                                                        onChange={handleSelectedExistingBodyDiscomfort}
                                                        labelKey="label"
                                                        valueKey="value"
                                                    />
                                                </Box>
                                            </Stack>
                                        </Stack>
                                    </CardContent>
                                </MLCard>
                            </Grid>

                            <Grid item md={6} xs={12}>
                                <RequestParticularForm
                                    {...particularsProps}
                                    enabledOptionalFields={enabledOptionalFields}
                                    mandatoryFields={mandatoryFields}
                                />
                            </Grid>

                            <Grid item md={6} xs={12}>
                                <OtherDetailsCard {...otherDetailsProps} />
                            </Grid>

                            <Grid item xs={12}>
                                <AssignCard {...assignCardProps} />
                            </Grid>

                            {(assignCardProps.checked && assignCardProps.selectedUserId !== undefined) ||
                                assignCardProps.anyPerson ? (
                                <Grid item xs={12}>
                                    <RequestAssessScheduleCard
                                        user_id={assignCardProps.selectedUserId ?? -1}
                                        employeeName={fullName}
                                        anyPerson={assignCardProps.anyPerson}
                                        {...scheduleCardProps}
                                    />
                                </Grid>
                            ) : null}
                        </Grid>

                        <Stack
                            width="100%"
                            direction="row"
                            justifyContent="center"
                            pt={4}
                            pb={3.5}
                            gap={2}
                        >
                            <MLButton
                                type="submit"
                                variant="contained"
                                color="secondary"
                                disabled={isRequestButtonDisabled()}
                                startIcon={isSubmitting ? <CircularProgress size={20} color="inherit" /> : undefined}
                            >
                                {isSubmitting ? "Submitting..." : "Request Ergo Assessment"}
                            </MLButton>
                            <MLButton
                                variant="outlined"
                                onClick={() => back()}
                                disabled={isSubmitting}
                            >
                                Cancel
                            </MLButton>
                        </Stack>
                    </MLContainer>
                </Stack>
            </form>
        </>
    );
};

export default RequestAssessment;