import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { useCreate, useUpdate, useOne, useGetIdentity, useList, BaseKey } from "@refinedev/core";
import { useParams, useNavigate } from "react-router-dom";
import {
  Box, Grid, MenuItem, FormControl,
  Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle,
  Stack
} from "@mui/material";
import MLButton from "../../components/ui/MLButton/MLButton";
import MLInputbox from "../../components/ui/MLInputbox/MLInputbox";
import MLSingleSelect from "../../components/ui/MLSingleSelecttag/MLSingleSelect";
import MessageBox from "./MessageBox";
import Loading from "../Loading/Loading";
import MLTypography from '../../components/ui/MLTypography/MLTypography';
import { API_URL } from '../../constants';
import axios from "axios";
import { Organization } from '../../models/Organization';
import CountryCodeAutocomplete, { CountryType } from '../../components/autocomplete/CountryCodeAutocomplete/CountryCodeAutocomplete';
import { countries } from '../../components/autocomplete/CountryCodeAutocomplete/countries';
import { v4 as uuidv4 } from 'uuid';

interface Address {
  id: number | string;
  address: string;
}

interface Role {
  id: number | string;
  name: string;
  description: string;
  type: string;
}

interface User {
  id: number | string;
  username: string;
  email: string;
  phone?: string;
  role?: Role;
  organization?: Organization;
  vendorAssignment?: {
    clientOrganizations: Organization[];
  };
  confirmed: boolean;
}

interface Employee {
  id: number | string;
  name: string;
  email: string;
  contact: string;
  organization?: Organization;
  address?: Address;
  userID?: User;
  countryCode?: number;
}

interface EmployeeDetails {
  name: string;
  email: string;
  contact: string;
  role: string;
  organization: string | number;
  address: string | number;
  countryCode?: number; // Added countryCode to interface
}

interface RolesResponse extends Record<string, any> {
  roles: Role[];
}

const INITIAL_EMPLOYEE_STATE: EmployeeDetails = {
  name: "",
  email: "",
  contact: "",
  role: "",
  organization: "",
  address: "",
  countryCode: 0 // Default value for country code
};

const EmployeeParticularsForm = () => {
  const { id: employeeId } = useParams();
  const navigate = useNavigate();
  const [error, setError] = useState<string>("");
  const [openSuccessPopup, setOpenSuccessPopup] = useState<boolean>(false);
  const [employeeDetails, setEmployeeDetails] = useState<EmployeeDetails>(INITIAL_EMPLOYEE_STATE);
  const [isEmailConfirmed, setIsEmailConfirmed] = useState<boolean>(false);
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [fieldErrors, setFieldErrors] = useState<{
    name?: string;
    email?: string;
    contact?: string;
  }>({});
  const [countryCode, setCountryCode] = useState<CountryType>(countries[196]); // Default to index 196 (e.g., Singapore)

  // Input field limits following web standards
  const NAME_MAX_LENGTH = 70;      // Standard for full name
  const EMAIL_MAX_LENGTH = 254;    // RFC 5321 standard
  const CONTACT_MAX_LENGTH = 15;   // International phone numbers

  // API hooks
  const { data: userIdentity } = useGetIdentity<{ id: string | number }>();
  const { mutate: mutateCreate, isLoading: createLoading } = useCreate();
  const { mutate: updateEmployee, isLoading: updateLoading } = useUpdate();

  // Data loading with optimized population
  const { data: currentUserData, isLoading: isLoadingCurrentUser } = useOne<User>({
    resource: "users",
    id: userIdentity?.id,
    meta: {
      populate: ["organization", "role", "vendorAssignment.clientOrganizations"]
    },
    queryOptions: {
      enabled: !!userIdentity?.id
    }
  });

  // Load employee data if editing
  const { data: completeEmployeeData, isLoading: isLoadingEmployee } = useOne<Employee>({
    resource: "employees",
    id: employeeId,
    meta: {
      populate: ["organization.addresses", "address", "userID"]
    },
    queryOptions: {
      enabled: !!employeeId
    }
  });

  // Load user data for role
  const { data: userData, isLoading: isUserLoading } = useOne<User>({
    resource: "users",
    id: completeEmployeeData?.data?.userID?.id,
    meta: { populate: "role" },
    queryOptions: {
      enabled: !!completeEmployeeData?.data?.userID?.id
    }
  });

  // Load roles
  const { data: rolesData, isLoading: isLoadingRoles } = useList<RolesResponse>({
    resource: "users-permissions/roles",
    queryOptions: {
      enabled: true,
    }
  });

  // Load organization addresses
  const { data: addressData } = useOne({
    resource: "organizations",
    id: employeeDetails.organization,
    meta: { populate: "addresses" },
    queryOptions: {
      enabled: !!employeeDetails.organization,
    }
  });

  // Memoized values
  const hasVendorAccess = useMemo(() =>
    !!(currentUserData?.data?.vendorAssignment),
    [currentUserData?.data]
  );

  const organizations = useMemo(() =>
    hasVendorAccess
      ? currentUserData?.data?.vendorAssignment?.clientOrganizations || []
      : [],
    [currentUserData?.data, hasVendorAccess]
  );

  const roles = useMemo(() => {
    const rolesList = (rolesData?.data as unknown as RolesResponse)?.roles || [];
    return rolesList.filter((role: Role) =>
      !['public', 'authenticated'].includes(role.name.toLowerCase())
    );
  }, [rolesData]);

  // Load addresses when organization changes
  const loadAddresses = useCallback(async () => {
    if (employeeDetails.organization && addressData?.data?.addresses) {
      setAddresses(addressData.data.addresses);
      if (addressData.data.addresses.length === 1) {
        setEmployeeDetails(prev => ({
          ...prev,
          address: addressData.data.addresses[0].id
        }));
      }
    }
  }, [employeeDetails.organization, addressData?.data?.addresses]);

  // Handler for country code changes
  const handleCountryCodeChange = (event: React.SyntheticEvent, newValue: CountryType | null) => {
    if (newValue) {
      setCountryCode(newValue);
      // Update the employee details with the country code phone value
      setEmployeeDetails(prev => ({
        ...prev,
        countryCode: parseInt(newValue.phone)
      }));
    }
  };

  // Modified input handler with better validation
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name } = event.target;
    let value = event.target.value;

    // Apply character limits and validation based on field type
    if (name === 'name') {
      if (value.length > NAME_MAX_LENGTH) {
        value = value.slice(0, NAME_MAX_LENGTH);
        setFieldErrors(prev => ({
          ...prev,
          name: `Name cannot exceed ${NAME_MAX_LENGTH} characters`
        }));
      } else {
        setFieldErrors(prev => ({
          ...prev,
          name: value.trim() === "" ? "Name cannot be empty" : undefined
        }));
      }
    } else if (name === 'email') {
      if (value.length > EMAIL_MAX_LENGTH) {
        value = value.slice(0, EMAIL_MAX_LENGTH);
        setFieldErrors(prev => ({
          ...prev,
          email: `Email cannot exceed ${EMAIL_MAX_LENGTH} characters`
        }));
      } else {
        setFieldErrors(prev => ({
          ...prev,
          email: value.trim() === "" ? "Email cannot be empty" : undefined
        }));
      }
    } else if (name === 'contact') {
      // Remove any non-numeric characters
      value = value.replace(/[^0-9]/g, '');

      if (value.length > CONTACT_MAX_LENGTH) {
        value = value.slice(0, CONTACT_MAX_LENGTH);
        setFieldErrors(prev => ({
          ...prev,
          contact: `Contact number cannot exceed ${CONTACT_MAX_LENGTH} digits`
        }));
      } else {
        setFieldErrors(prev => ({
          ...prev,
          contact: value.trim() === "" ? "Contact number cannot be empty" : undefined
        }));
      }
    }

    // Update employee details with possibly truncated value
    setEmployeeDetails(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Separate handler for select fields
  const handleSelectChange = (field: keyof EmployeeDetails) => (event: { target: { value: unknown } }) => {
    setEmployeeDetails(prev => ({
      ...prev,
      [field]: event.target.value
    }));
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    if (employeeId) {
      // Update user details first before updating employee
      updateEmployee(
        {
          resource: "users",
          id: completeEmployeeData?.data?.userID?.id as BaseKey, // Assuming userID is populated
          values: {
            username: employeeDetails.name,
            email: employeeDetails.email,
            phone: employeeDetails.contact,
            role: employeeDetails.role, // Optional if roles are shared
            confirmed: true
          },
        },
        {
          onSuccess: () => {
            // After successful user update, update employee details
            updateEmployee(
              {
                resource: "employees",
                id: employeeId,
                values: {
                  name: employeeDetails.name,
                  email: employeeDetails.email,
                  contact: employeeDetails.contact,
                  role: employeeDetails.role,
                  organization: employeeDetails.organization,
                  address: employeeDetails.address,
                  countryCode: employeeDetails.countryCode // Include country code when updating
                },
              },
              {
                onSuccess: () => {
                  setError("");
                  setOpenSuccessPopup(true);
                },
                onError: (error) => {
                  setError("Failed to update employee record.");
                  // console.error("Employee Update Error:", error);
                },
              }
            );
          },
          onError: (error) => {
            setError("Failed to update user record.");
            // console.error("User Update Error:", error);
          },
        }
      );
    } else {
      // Create user first
      try {
        const { data, status } = await axios.post(
          `${API_URL}/api/auth/local/register`,
          {
            // username: employeeDetails.name,
            username: uuidv4(),
            email: employeeDetails.email,
            password: "Pass123*",
            role: employeeDetails.role,
            organization: employeeDetails.organization,
            phone: employeeDetails.contact,
            resetRequired: true,
            employeeView: true,
          }
        );
        if (status != 200) {
          setError("Failed to create user account.");
          // console.error("User Creation Error:", data.error.message);
        }
        if (status === 200) {
          mutateCreate(
            {
              resource: "employees",
              values: {
                name: employeeDetails.name,
                email: employeeDetails.email,
                contact: employeeDetails.contact,
                role: employeeDetails.role,
                organization: { id: employeeDetails.organization },
                address: employeeDetails.address,
                userID: data.user.id, // Link to created user,
                deskNo: '0',
                countryCode: employeeDetails.countryCode ,
                employeeView: true,
              },
            },
            {
              onSuccess: () => {
                setEmployeeDetails({
                  name: "",
                  email: "",
                  contact: "",
                  role: "",
                  organization: "",
                  address: "",
                  countryCode: 0
                });
                setError("");
                setOpenSuccessPopup(true);
              },
              onError: (createError) => {
                setError("Failed to create employee record.");
                // console.error("Employee Creation Error:", createError);
              },
            }
          );
        }
      } catch (e: any) {
        setError(`Failed to create user account. ${e.response.data.error.message}`);
        // console.error("User Creation Error:", e);
      }
    }
  };

  // Effects
  useEffect(() => {
    if (completeEmployeeData?.data && userData?.data) {
      const { name, email, contact, organization, address, countryCode } = completeEmployeeData.data;
      if (completeEmployeeData.data.userID) setIsEmailConfirmed(completeEmployeeData.data.userID.confirmed);

      // Set the employee details including country code
      setEmployeeDetails({
        name,
        email,
        contact,
        role: userData.data.role?.id?.toString() || '', // Set role from user data
        organization: organization?.id?.toString() || '',
        address: address?.id?.toString() || '',
        countryCode: countryCode || 0
      });

      // Find and set the country code object for the autocomplete
      if (countryCode) {
        const foundCountry = countries.find(country => parseInt(country.phone) === countryCode);
        if (foundCountry) {
          setCountryCode(foundCountry);
        }
      }
    }
  }, [completeEmployeeData?.data, userData?.data]);


  useEffect(() => {
    if (!hasVendorAccess && currentUserData?.data?.organization?.id) {
      setEmployeeDetails(prev => ({
        ...prev,
        organization: currentUserData.data.organization!.id
      }));
    }
  }, [hasVendorAccess, currentUserData?.data]);

  useEffect(() => {
    loadAddresses();
  }, [loadAddresses]);

  const isLoading = isLoadingCurrentUser ||
    (!!employeeId && (isLoadingEmployee || isUserLoading)) ||
    isLoadingRoles;

  if (isLoading) {
    return <Loading />;
  }

  return (
    <Box sx={{ display: "flex", flexDirection: "column", alignItems: "center" }}>
      <form onSubmit={handleSubmit}>
        <Grid container spacing={3} sx={{ maxWidth: 800 }}>
          <Grid item xs={12}>
            {/* Particulars Section */}
            <Box sx={{ border: "1px solid #ccc", borderRadius: "8px", p: 4, mb: 3 }}>
              <MLTypography variant="h2" gutterBottom mb={2}>
                Particulars
              </MLTypography>
              {error && <MessageBox errorMessage={error} found={false} />}

              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <MLInputbox
                    fullWidth
                    label="Full name"
                    name="name"
                    value={employeeDetails.name}
                    onChange={handleInputChange}
                    required
                    error={!!fieldErrors.name}
                    helperText={fieldErrors.name || `${employeeDetails.name.length}/${NAME_MAX_LENGTH}`}
                    inputProps={{
                      maxLength: NAME_MAX_LENGTH
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Stack direction={{ sm: "row", xs: "column" }} gap={2} alignItems="flex-start">
                    <CountryCodeAutocomplete
                      value={countryCode}
                      onChange={handleCountryCodeChange}
                    />
                    <Stack width={'100%'} direction={"row"}>
                      <Grid item xs={12}>
                        <MLInputbox
                          fullWidth
                          label="Mobile no."
                          name="contact"
                          type="tel"
                          value={employeeDetails.contact}
                          onChange={handleInputChange}
                          required
                          error={!!fieldErrors.contact}
                          helperText={fieldErrors.contact || `${employeeDetails.contact.length}/${CONTACT_MAX_LENGTH}`}
                          inputProps={{
                            maxLength: CONTACT_MAX_LENGTH,
                            pattern: "[0-9]*"
                          }}
                        />
                      </Grid>
                    </Stack>
                  </Stack>
                </Grid>

                <Grid item xs={12}>
                  <MLInputbox
                    fullWidth
                    label="Email address"
                    name="email"
                    type="email"
                    disabled={isEmailConfirmed}
                    value={employeeDetails.email}
                    onChange={handleInputChange}
                    required
                    error={!!fieldErrors.email}
                    // helperText={fieldErrors.email || `${employeeDetails.email.length}/${EMAIL_MAX_LENGTH}`}
                    inputProps={{
                      maxLength: EMAIL_MAX_LENGTH
                    }}
                  />
                </Grid>
              </Grid>
            </Box>

            {/* Work details Section */}
            <Box sx={{ border: "1px solid #ccc", borderRadius: "8px", p: 4, mb: 3 }}>
              <MLTypography variant="h2" mb={2} gutterBottom>
                Work details
              </MLTypography>
              <Grid container spacing={2.5}>
                <Grid item xs={12} mb={1}>
                  <FormControl fullWidth required>
                    {hasVendorAccess ? (
                      <MLSingleSelect
                        heading="Company"
                        name="company"
                        placeholder="Select company's name"
                        value={employeeDetails.organization}
                        onChange={handleSelectChange('organization')}
                        fullWidth
                      >
                        {organizations.map((org) => (
                          <MenuItem key={org.id} value={org.id}>
                            {org.name}
                          </MenuItem>
                        ))}
                      </MLSingleSelect>
                    ) : (
                      <MLInputbox
                        fullWidth
                        label="Company"
                        value={currentUserData?.data?.organization?.name || ''}
                        disabled
                      />
                    )}
                  </FormControl>
                </Grid>

                <Grid item xs={12} mb={1}>
                  <FormControl fullWidth required>
                    {addresses.length > 1 ? (
                      <MLSingleSelect
                        heading="Company Address"
                        name="address"
                        placeholder="Select company's address"
                        value={employeeDetails.address}
                        onChange={handleSelectChange('address')}
                        fullWidth
                      >
                        {addresses.map((addr) => (
                          <MenuItem key={addr.id} value={addr.id}>
                            {addr.address}
                          </MenuItem>
                        ))}
                      </MLSingleSelect>
                    ) : (
                      <MLInputbox
                        fullWidth
                        label="Company Address"
                        value={addresses[0]?.address || ''}
                        disabled
                      />
                    )}
                  </FormControl>
                </Grid>

                {currentUserData?.data?.role?.name === "Administrator" && (
                  <Grid item xs={12}>
                    <FormControl fullWidth required>
                      <MLSingleSelect
                        heading="Role"
                        placeholder="Select role"
                        value={employeeDetails.role}
                        onChange={handleSelectChange('role')}
                        fullWidth
                      >
                        {roles.map((role) => (
                          <MenuItem key={role.id} value={role.id}>
                            {role.name}
                          </MenuItem>
                        ))}
                      </MLSingleSelect>
                    </FormControl>
                  </Grid>
                )}
              </Grid>
            </Box>

            {/* Action Buttons */}
            <Box sx={{ display: "flex", justifyContent: "center", gap: 2, mt: 5 }}>
              <MLButton
                type="submit"
                variant="contained"
                color="secondary"
                disabled={createLoading || updateLoading}
              >
                {employeeId ? "Update profile" : "Create employee"}
              </MLButton>
              <MLButton
                onClick={() => navigate("/employees")}
                size="medium"
                variant="outlined"
              >
                Cancel
              </MLButton>
            </Box>
          </Grid>
        </Grid>
      </form>

      {/* Success Dialog */}
      <Dialog
        open={openSuccessPopup}
        onClose={() => {
          setOpenSuccessPopup(false);
          navigate("/employees");
        }}
      >
        <DialogTitle fontSize={26}>Success</DialogTitle>
        <DialogContent>
          <DialogContentText
            fontSize={16}
            fontFamily={"Work Sans"}
            color={"rgba(0, 0, 0, 0.87)"}
            fontWeight={500}
          >
            {employeeId ? "Profile updated successfully" : "Employee created successfully"}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <MLButton
            size="medium"
            sx={{ mx: 2 }}
            variant="contained"
            color="secondary"
            onClick={() => {
              setOpenSuccessPopup(false);
              navigate("/employees");
            }}
          >
            OK
          </MLButton>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default EmployeeParticularsForm;