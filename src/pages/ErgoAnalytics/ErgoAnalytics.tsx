import MLBanner from "../../components/ui/MLBanner/MLBanner";
import MLButton from "../../components/ui/MLButton/MLButton";
import MLContainer from "../../components/ui/MLMaxWidthContainer/MLMaxWidthContainer";
import MLTypography from "../../components/ui/MLTypography/MLTypography";
import { desktop, tablet } from "../../responsiveStyles";
import Loading from "../Loading/Loading";
import IIdentity from "../SelfAssessment/models/IIdentity";
import DiscomfortArea from "./DiscomfortArea";
import ErgoAnalyticsHeader from "./ErgoAnalyticsHeader";
import RecommendationTypes from "./RecommendationTypes";
import AssessmentType from "./components/AssessmentType";
import DetailImplementationBreakdown from "./components/DetailImplementationBreakdown";
import EscalationRequest from "./components/EscalationRequest";
import IssueIdentifiedType from "./components/IssueIdentifiedType";
import IssueResolution from "./components/IssueResolution";
import IssueResolutionCompliance from "./components/IssueResolutionCompliance";
import NoOfEmployees from "./components/NoOfEmployees";
import PosturalIssueTrend from "./components/PosturalIssueTrend";
import RiskLevel from "./components/RiskLevel";
import RootCauseAnalysis from "./components/RootCauseAnalysis";
import AddIcon from "@mui/icons-material/Add";
import { Box, Divider, keyframes, Stack } from "@mui/material";
import { useGo, useList, useOne } from "@refinedev/core";
import { useGetIdentity } from "@refinedev/core";
import dayjs from "dayjs";
import isoWeek from "dayjs/plugin/isoWeek";
import { useMemo, useState } from "react";
import { Link } from "react-router-dom";

dayjs.extend(isoWeek);

export interface ITotalDiscomforts {
  feet: number;
  head: number;
  eyes: number;
  neck: number;
  knees: number;
  elbows: number;
  wrists: number;
  fingers: number;
  forearms: number;
  lowerBack: number;
  shoulders: number;
}

export interface ITotalRisks {
  lowRisk: number;
  highRisk: number;
  mediumRisk: number;
}

export interface ICaseDistribution {
  selfAssessment: {
    general: number;
    discomfort: number;
  };
  expertAssessment: {
    general: number;
    discomfort: number;
    accommodation: number;
    office: number;
  };
}

export interface IRecommendationTypes {
  setup: number;
  products: number;
  habits: number;
}

enum MonthEnum {
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sep",
  "Oct",
  "Nov",
  "Dec",
}

// consolidates all entries within a date range
const mergeErgoAnalytics = (
  dataList: any[],
): {
  byMonth: Record<
    string,
    {
      totalAssessments: number;
      totalRisks: ITotalRisks;
      totalDiscomforts: ITotalDiscomforts;
      caseDistribution: ICaseDistribution;
      totalCoveredEmployees: number;
      rootCauseCounts: Record<string, number>;
      rootCauseAnalysis: Record<string, { rootCause: string[] }>;
      issues: {
        resolved: number;
        identified: number;
        helpNeeded: number;
      };
    }
  >;
  totals: {
    totalAssessments: number;
    caseDistribution: ICaseDistribution;
    rootCauseCounts: Record<string, number>;
    rootCauseAnalysis: Record<string, { rootCause: string[] }>;
    issues: {
      resolved: number;
      identified: number;
      helpNeeded: number;
    };
  };
} => {
  // group data by month
  const groupedByMonth: Record<string, any[]> = dataList.reduce(
    (groups, item) => {
      // extract month from the date string
      const date = new Date(item.date);
      const monthKey = MonthEnum[date.getMonth()];

      // create month group if it doesn't exist
      if (!groups[monthKey]) {
        groups[monthKey] = [];
      }

      // add the item to its month group
      groups[monthKey].push(item);

      return groups;
    },
    {},
  );

  // init aggregated totals
  const aggregatedTotals = {
    totalAssessments: 0,
    caseDistribution: {
      selfAssessment: {
        general: 0,
        discomfort: 0,
      },
      expertAssessment: {
        general: 0,
        discomfort: 0,
        accommodation: 0,
        office: 0,
      },
    },
    rootCauseCounts: {} as Record<string, number>,
    rootCauseAnalysis: {} as Record<string, { rootCause: string[] }>,
    issues: {
      identified: 0,
      resolved: 0,
      helpNeeded: 0,
    },
  };

  // for each month, apply consolidation logic
  const resultByMonth: Record<string, any> = {};

  for (const [month, monthData] of Object.entries(groupedByMonth)) {
    resultByMonth[month] = monthData.reduce(
      (acc, curr) => {
        acc.totalAssessments =
          (acc.totalAssessments || 0) + curr.totalAssessments;
        acc.totalCoveredEmployees = Math.max(
          acc.totalCoveredEmployees || 0,
          curr.totalCoveredEmployees,
        );

        acc.totalRisks = {
          lowRisk: (acc.totalRisks?.lowRisk || 0) + curr.totalRisks.lowRisk,
          highRisk: (acc.totalRisks?.highRisk || 0) + curr.totalRisks.highRisk,
          mediumRisk:
            (acc.totalRisks?.mediumRisk || 0) + curr.totalRisks.mediumRisk,
        };

        acc.totalDiscomforts = {
          feet:
            (acc.totalDiscomforts?.feet || 0) +
            (curr.totalDiscomforts?.feet || 0),
          head:
            (acc.totalDiscomforts?.head || 0) +
            (curr.totalDiscomforts?.head || 0),
          neck:
            (acc.totalDiscomforts?.neck || 0) +
            (curr.totalDiscomforts?.neck || 0),
          knees:
            (acc.totalDiscomforts?.knees || 0) +
            (curr.totalDiscomforts?.knees || 0),
          elbows:
            (acc.totalDiscomforts?.elbows || 0) +
            (curr.totalDiscomforts?.elbows || 0),
          wrists:
            (acc.totalDiscomforts?.wrists || 0) +
            (curr.totalDiscomforts?.wrists || 0),
          fingers:
            (acc.totalDiscomforts?.fingers || 0) +
            (curr.totalDiscomforts?.fingers || 0),
          forearms:
            (acc.totalDiscomforts?.forearms || 0) +
            (curr.totalDiscomforts?.forearms || 0),
          lowerBack:
            (acc.totalDiscomforts?.lowerBack || 0) +
            (curr.totalDiscomforts?.lowerBack || 0),
          shoulders:
            (acc.totalDiscomforts?.shoulders || 0) +
            (curr.totalDiscomforts?.shoulders || 0),
          eyes:
            (acc.totalDiscomforts?.eyes || 0) +
            (curr.totalDiscomforts?.eyes || 0),
        };

        const currentCaseDistribution = curr.caseDistribution || {
          selfAssessment: { general: 0, discomfort: 0 },
          expertAssessment: {
            general: 0,
            discomfort: 0,
            accommodation: 0,
            office: 0,
          },
        };

        acc.caseDistribution = {
          selfAssessment: {
            general:
              (acc.caseDistribution?.selfAssessment?.general || 0) +
              (currentCaseDistribution.selfAssessment?.general || 0),
            discomfort:
              (acc.caseDistribution?.selfAssessment?.discomfort || 0) +
              (currentCaseDistribution.selfAssessment?.discomfort || 0),
          },
          expertAssessment: {
            general:
              (acc.caseDistribution?.expertAssessment?.general || 0) +
              (currentCaseDistribution.expertAssessment?.general || 0),
            discomfort:
              (acc.caseDistribution?.expertAssessment?.discomfort || 0) +
              (currentCaseDistribution.expertAssessment?.discomfort || 0),
            accommodation:
              (acc.caseDistribution?.expertAssessment?.accommodation || 0) +
              (currentCaseDistribution.expertAssessment?.accommodation || 0),
            office:
              (acc.caseDistribution?.expertAssessment?.office || 0) +
              (currentCaseDistribution.expertAssessment?.office || 0),
          },
        };

        // Initialize root cause objects if not present
        acc.rootCauseCounts = acc.rootCauseCounts || {};
        acc.rootCauseAnalysis = acc.rootCauseAnalysis || {};

        // Merge root cause data
        if (curr.rootCause) {
          // Handle counts
          if (curr.rootCause.counts) {
            Object.entries(curr.rootCause.counts).forEach(([cause, count]) => {
              acc.rootCauseCounts[cause] =
                (acc.rootCauseCounts[cause] || 0) + Number(count);
            });
          }

          // Handle analysis
          if (curr.rootCause.analysis) {
            Object.entries(curr.rootCause.analysis).forEach(
              ([bodyPart, data]) => {
                if (!acc.rootCauseAnalysis[bodyPart]) {
                  acc.rootCauseAnalysis[bodyPart] = { rootCause: [] };
                }

                // Type assertion to ensure we know what we're working with
                const typedData = data as { rootCause: string[] };
                if (Array.isArray(typedData.rootCause)) {
                  typedData.rootCause.forEach((cause: string) => {
                    if (
                      !acc.rootCauseAnalysis[bodyPart].rootCause.includes(cause)
                    ) {
                      acc.rootCauseAnalysis[bodyPart].rootCause.push(cause);
                    }
                  });
                }
              },
            );
          }
        }

        acc.issues = {
          resolved: (acc.issues?.resolved || 0) + curr.issues?.resolved || 0,
          identified:
            (acc.issues?.identified || 0) + curr.issues?.identified || 0,
          helpNeeded:
            (acc.issues?.helpNeeded || 0) + curr.issues?.helpNeeded || 0,
        };

        return acc;
      },
      {} as {
        totalAssessments: number;
        totalRisks: ITotalRisks;
        totalDiscomforts: ITotalDiscomforts;
        caseDistribution: ICaseDistribution;
        rootCauseCounts: Record<string, number>;
        rootCauseAnalysis: Record<string, { rootCause: string[] }>;
        totalCoveredEmployees: number;
        issues: {
          resolved: number;
          identified: number;
          helpNeeded: number;
        };
      },
    );

    // update aggregated totals with data from this month
    aggregatedTotals.totalAssessments += resultByMonth[month].totalAssessments;

    // aggregate case distribution data
    aggregatedTotals.caseDistribution.selfAssessment.general +=
      resultByMonth[month].caseDistribution.selfAssessment.general || 0;
    aggregatedTotals.caseDistribution.selfAssessment.discomfort +=
      resultByMonth[month].caseDistribution.selfAssessment.discomfort || 0;

    // aggregate issues data
    aggregatedTotals.issues.resolved +=
      resultByMonth[month].issues.resolved || 0;
    aggregatedTotals.issues.identified +=
      resultByMonth[month].issues.identified || 0;
    aggregatedTotals.issues.helpNeeded +=
      resultByMonth[month].issues.helpNeeded || 0;

    // handle expert assessment which might be empty in some entries
    if (resultByMonth[month].caseDistribution.expertAssessment) {
      aggregatedTotals.caseDistribution.expertAssessment.general +=
        resultByMonth[month].caseDistribution.expertAssessment.general || 0;
      aggregatedTotals.caseDistribution.expertAssessment.discomfort +=
        resultByMonth[month].caseDistribution.expertAssessment.discomfort || 0;
      aggregatedTotals.caseDistribution.expertAssessment.accommodation +=
        resultByMonth[month].caseDistribution.expertAssessment.accommodation ||
        0;
      aggregatedTotals.caseDistribution.expertAssessment.office +=
        resultByMonth[month].caseDistribution.expertAssessment.office || 0;
    }

    // Merge root cause counts into aggregated totals
    if (resultByMonth[month].rootCauseCounts) {
      Object.entries(resultByMonth[month].rootCauseCounts).forEach(
        ([cause, count]) => {
          aggregatedTotals.rootCauseCounts[cause] =
            (aggregatedTotals.rootCauseCounts[cause] || 0) + Number(count);
        },
      );
    }

    // Merge root cause analysis into aggregated totals
    if (resultByMonth[month].rootCauseAnalysis) {
      Object.entries(resultByMonth[month].rootCauseAnalysis).forEach(
        ([bodyPart, data]) => {
          if (!aggregatedTotals.rootCauseAnalysis[bodyPart]) {
            aggregatedTotals.rootCauseAnalysis[bodyPart] = { rootCause: [] };
          }

          // Type assertion to ensure we know what we're working with
          const typedData = data as { rootCause: string[] };
          if (Array.isArray(typedData.rootCause)) {
            typedData.rootCause.forEach((cause: string) => {
              if (
                !aggregatedTotals.rootCauseAnalysis[
                  bodyPart
                ].rootCause.includes(cause)
              ) {
                aggregatedTotals.rootCauseAnalysis[bodyPart].rootCause.push(
                  cause,
                );
              }
            });
          }
        },
      );
    }
  }

  return {
    byMonth: resultByMonth,
    totals: aggregatedTotals,
  };
};

const groupExpertAssessmentStatus = (dataList: any[]) => {
  const inProgressStatusEnums = [
    "assigned",
    "scheduled",
    "assessment_in_progress",
    "assessment_completed",
    "report_created",
    "pending_approval",
    "report_shared",
    "follow_up",
  ];

  return dataList.reduce(
    (acc, curr) => {
      if (curr.status === "created") {
        acc.open = (acc.open || 0) + 1;
      }
      if (inProgressStatusEnums.includes(curr.status)) {
        acc.inProgress = (acc.inProgress || 0) + 1;
      }
      if (curr.status === "closed") {
        acc.closed = (acc.closed || 0) + 1;
      }

      return acc;
    },
    {} as {
      Open: number;
      "In-progress": number;
      Closed: number;
    },
  );
};

const transformDataForRiskLevel = (
  monthlyData: Record<string, any>,
): Array<{
  month: string;
  low: number;
  medium: number;
  high: number;
}> => {
  // Define all months in order
  const allMonths = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  // Create the result array with default values (0) for all months
  const result = allMonths.map((month) => ({
    month,
    low: 0,
    medium: 0,
    high: 0,
  }));

  // Fill in the data for months that exist in the input
  for (const [month, data] of Object.entries(monthlyData)) {
    // Find the index of this month in our result array
    const monthIndex = allMonths.indexOf(month);

    if (monthIndex !== -1 && data.totalRisks) {
      // Calculate the total number of risks
      const totalRisks =
        (data.totalRisks.lowRisk || 0) +
        (data.totalRisks.mediumRisk || 0) +
        (data.totalRisks.highRisk || 0);

      if (totalRisks > 0) {
        // Calculate percentages (rounded to 1 decimal place)
        result[monthIndex].low =
          Math.round(((data.totalRisks.lowRisk || 0) / totalRisks) * 1000) / 10;
        result[monthIndex].medium =
          Math.round(((data.totalRisks.mediumRisk || 0) / totalRisks) * 1000) /
          10;
        result[monthIndex].high =
          Math.round(((data.totalRisks.highRisk || 0) / totalRisks) * 1000) /
          10;
      }
      // If totalRisks is 0, we'll keep the default values of 0
    }
  }

  return result;
};

const transformDataForIssueTrend = (
  monthlyData: Record<string, any>,
): Array<{
  month: string;
  identified: number;
  resolved: number;
}> => {
  const allMonths = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  const result = allMonths.map((month) => ({
    month,
    identified: 0,
    resolved: 0,
  }));

  for (const [month, data] of Object.entries(monthlyData)) {
    const monthIndex = allMonths.indexOf(month);
    result[monthIndex].identified = data.issues.identified;
    result[monthIndex].resolved = data.issues.resolved;
  }

  return result;
};

const ErgoAnalytics = () => {
  const { data: user } = useGetIdentity<IIdentity>();
  const go = useGo();

  const {
    data: employeesData,
    isLoading: isEmployeesDataLoading,
    isError: isEmployeeError,
  } = useOne({
    resource: "users",
    id: user?.id,
    meta: {
      fields: ["id"],
      populate: {
        organization: {
          fields: ["name", "typeOfRecommendation"],
          populate: {
            employees: {
              fields: [
                "hasCompletedAssessment",
                "issuesIdentified",
                "issuesResolved",
              ],
              populate: {
                selfAssessmentCases: {
                  fields: ["isCompleted", "createdAt", "updatedAt"],
                  populate: {
                    selfAssessmentReport: {
                      fields: ["assessmentType"],
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  });
  const employees = useMemo(() => {
    return employeesData ? employeesData.data.organization.employees : [];
  }, [employeesData]);
  const organization = employeesData ? employeesData.data.organization : {};

  const employeesResolutionCompliance = useMemo(() => {
    const compliance = {
      highProgress: 0,
      mediumProgress: 0,
      needsAttention: 0,
    };
    if (employees.length == 0) return compliance;
    employees.forEach(
      (e: {
        issuesIdentified: number | null;
        issuesResolved: number | null;
      }) => {
        if (e.issuesIdentified == null || e.issuesResolved == null) return;
        const progress = Math.round(
          (e.issuesResolved / e.issuesIdentified) * 100,
        );
        if (progress < 25) return (compliance.needsAttention += 1); // 0 to 24%
        if (progress >= 25 && progress < 75)
          return (compliance.mediumProgress += 1); // 25 to 74%
        if (progress >= 75) return (compliance.highProgress += 1); // 75 to 100%
      },
    );

    return compliance;
  }, [employees]);

  const notStartedSelfAssessments = useMemo(() => {
    // employee has not started or completed any self assessment
    if (employees.length == 0) return null;
    return employees.filter(
      (e: any) =>
        e.selfAssessmentCases.length === 0 && !e.hasCompletedAssessment,
    );
  }, [employees]);

  const startedSelfAssessments = useMemo(() => {
    if (employees.length == 0) return null;
    return employees.filter(
      (e: any) =>
        e.selfAssessmentCases.length > 0 &&
        e.selfAssessmentCases.some((c: any) => !c.isCompleted) &&
        !e.hasCompletedAssessment,
    );
  }, [employees]);

  const completedSelfAssessments = useMemo(() => {
    if (employees.length == 0) return null;
    return employees.filter((e: any) => e.hasCompletedAssessment);
  }, [employees]);

  const {
    data: ergoAnalyticsData,
    isLoading: isErgoAnalyticsDataLoading,
    isError: isErgoAnalyticsError,
  } = useList({
    resource: "ergo-analytics",
    filters: [
      {
        field: "company",
        operator: "eq",
        value: organization.name,
      },
      {
        field: "date",
        operator: "gte",
        value: dayjs().startOf("year").format("YYYY-MM-DD"),
      },
      {
        field: "date",
        operator: "lte",
        value: dayjs().endOf("year").format("YYYY-MM-DD"),
      },
    ],
    pagination: { pageSize: 366, mode: "server" },
  });
  const ergoAnalyticsMemo = useMemo(() => {
    if (!ergoAnalyticsData?.data || ergoAnalyticsData.data.length === 0)
      return null;

    const mergedErgoAnalytics = mergeErgoAnalytics(ergoAnalyticsData.data);
    return Object.values(mergedErgoAnalytics).length > 0
      ? mergedErgoAnalytics
      : null;
  }, [ergoAnalyticsData?.data]);

  const riskLevelMemo = useMemo(() => {
    if (!ergoAnalyticsMemo?.byMonth) return [];
    return transformDataForRiskLevel(ergoAnalyticsMemo.byMonth);
  }, [ergoAnalyticsMemo?.byMonth]);

  const issueTrendMemo = useMemo(() => {
    if (!ergoAnalyticsMemo?.byMonth) return [];
    return transformDataForIssueTrend(ergoAnalyticsMemo.byMonth);
  }, [ergoAnalyticsMemo?.byMonth]);

  const rootCauseAnalysisMemo = useMemo(() => {
    if (!ergoAnalyticsMemo?.totals.rootCauseAnalysis) return {};

    const rootCauseAnalysis: { [bodyPart: string]: string } = {};
    Object.entries(ergoAnalyticsMemo?.totals.rootCauseAnalysis).forEach(
      ([key, value]) => {
        rootCauseAnalysis[key] = value.rootCause.join(", ");
      },
    );

    return rootCauseAnalysis;
  }, [ergoAnalyticsMemo?.totals.rootCauseAnalysis]);

  // convert root cause counts into percentage view
  const rootCauseCountMemo = useMemo(() => {
    if (!ergoAnalyticsMemo?.totals.rootCauseCounts) return [];

    // Calculate the sum of all values
    const sum = Object.values(ergoAnalyticsMemo.totals.rootCauseCounts).reduce(
      (acc, val) => acc + val,
      0,
    );

    // Convert to percentages
    const percentages = Object.entries(
      ergoAnalyticsMemo.totals.rootCauseCounts,
    ).map(([key, value]) => ({
      label: key,
      value: Math.round((value / sum) * 100),
    }));

    return percentages;
  }, [ergoAnalyticsMemo?.totals.rootCauseCounts]);

  if (
    isEmployeesDataLoading ||
    isErgoAnalyticsDataLoading ||
    isEmployeesDataLoading
  ) {
    <Loading />;
  }

  if (isEmployeeError || isErgoAnalyticsError) {
    <Stack>Server Unavailable, please try later</Stack>;
  }

  return (
    <>
      {/* <MLBanner backgroundColor='#E3DDFF' title={"Ergo Analytics"} /> */}
      <Stack
        direction="column"
        height="100%"
        sx={{

          paddingBottom: "64px",
        }}
        gap="30px"
      >
        <MLContainer usePaddingY>
          <Stack direction="row" justifyContent="space-between">
            <ErgoAnalyticsHeader />
            <Stack direction="row" justifyContent={"flex-end"}>
              <Stack direction="row" gap={1} alignItems={"center"}>
                <MLButton
                  type="submit"
                  variant="contained"
                  color="secondary"
                  onClick={() => go({ to: "/aicase/create" })}
                >
                  Begin Assessment
                </MLButton>
                {/* <Link to="cases/create">
                  <MLButton
                    endIcon={<AddIcon />}
                    variant="contained"
                    color="secondary"
                  >
                    Create case
                  </MLButton>
                </Link> */}
              </Stack>
            </Stack>
          </Stack>
          <Stack direction={{ sm: "column", md: "row" }} gap={"30px"}>
            {/** No of employees */}
            {isEmployeesDataLoading ? (
              <Stack
                sx={{
                  padding: "20px",
                  gap: "25px",
                  border: "0.5px solid #9C9C9C",
                  borderRadius: "10px",
                  flex: 1,
                }}
              >
                <Loading />
              </Stack>
            ) : (
              <NoOfEmployees
                totalEmployees={employees ? employees.length : 0}
                completed={
                  completedSelfAssessments ? completedSelfAssessments.length : 0
                }
                started={
                  startedSelfAssessments ? startedSelfAssessments.length : 0
                }
                notStarted={
                  notStartedSelfAssessments
                    ? notStartedSelfAssessments.length
                    : 0
                }
              />
            )}

            {/** Assessment Type */}
            {isErgoAnalyticsDataLoading ? (
              <Stack
                sx={{
                  padding: "20px",
                  gap: "25px",
                  border: "0.5px solid #9C9C9C",
                  borderRadius: "10px",
                  flex: 1,
                }}
              >
                <Loading />
              </Stack>
            ) : (
              <AssessmentType
                totalAssessment={
                  ergoAnalyticsMemo
                    ? ergoAnalyticsMemo.totals.totalAssessments
                    : 0
                }
                discomfort={
                  ergoAnalyticsMemo
                    ? ergoAnalyticsMemo.totals.caseDistribution.selfAssessment
                      .discomfort
                    : 0
                }
                general={
                  ergoAnalyticsMemo
                    ? ergoAnalyticsMemo.totals.caseDistribution.selfAssessment
                      .general
                    : 0
                }
              />
            )}
          </Stack>

          <Stack
            direction={{ sm: "column", md: "row" }}
            marginTop={"50px"}
            gap={"30px"}
          >
            {/** Risk level */}
            <Stack flex={1}>
              <RiskLevel
                data={riskLevelMemo}
                isLoading={isErgoAnalyticsDataLoading}
              />
            </Stack>

            {/** Escalation request */}
            <Stack flex={1}>
              <MLTypography
                variant="h1"
                fontSize={"24px"}
                fontWeight={700}
                lineHeight={1.2}
                marginBottom={"50px"}
              >
                Escalation request
              </MLTypography>
              <EscalationRequest
                responseTime={{ selfAssessment: 7, employeeRequest: 4 }} // placeholder data, actual value not available yet
              />
            </Stack>
          </Stack>

          <Divider
            sx={{
              marginY: "35px",
            }}
          />

          <Stack
            direction={{ sm: "column", md: "row" }}
            gap={"30px"}
            minHeight={"600px"}
          >
            {/** Area of discomfort */}
            <Stack flex={1}>
              {isErgoAnalyticsDataLoading ? (
                <Loading />
              ) : (
                <DiscomfortArea
                  totalDiscomforts={
                    ergoAnalyticsMemo &&
                      ergoAnalyticsMemo.byMonth &&
                      ergoAnalyticsMemo.byMonth[MonthEnum[new Date().getMonth()]]
                      ? ergoAnalyticsMemo.byMonth[
                        MonthEnum[new Date().getMonth()]
                      ].totalDiscomforts
                      : undefined
                  }
                />
              )}
            </Stack>

            {/** Root cause analysis */}
            <Stack flex={1}>
              {isErgoAnalyticsDataLoading ? (
                <Loading />
              ) : (
                <RootCauseAnalysis data={rootCauseAnalysisMemo} />
              )}
            </Stack>
          </Stack>

          <Stack
            direction={{ sm: "column", md: "row" }}
            marginTop={"50px"}
            gap={"30px"}
            minHeight={"400px"}
          >
            <Stack flex={1} gap={"30px"}>
              {/** Issues and resolution */}
              {isErgoAnalyticsDataLoading ? (
                <Loading />
              ) : (
                <IssueResolution
                  issueIdentified={
                    ergoAnalyticsMemo
                      ? ergoAnalyticsMemo.totals.issues.identified
                      : 0
                  }
                  issueResolved={
                    ergoAnalyticsMemo
                      ? ergoAnalyticsMemo.totals.issues.resolved
                      : 0
                  }
                  helpNeeded={
                    ergoAnalyticsMemo
                      ? ergoAnalyticsMemo.totals.issues.helpNeeded
                      : 0
                  }
                />
              )}
            </Stack>

            {/** Postural Issue Trend */}
            <Stack flex={1}>
              <PosturalIssueTrend
                data={issueTrendMemo}
                isLoading={isErgoAnalyticsDataLoading}
              />
            </Stack>
          </Stack>

          <Stack
            direction={{ sm: "column", md: "row" }}
            marginTop={"50px"}
            gap={"30px"}
          >
            {/** Type of issues identified - not mobile responsive */}
            <Stack flex={1}>
              <IssueIdentifiedType data={rootCauseCountMemo} />
            </Stack>

            {/** Type of recommendation - not mobile responsive */}
            <Stack flex={1}>
              {isEmployeesDataLoading ? (
                <Loading />
              ) : (
                <>
                  <MLTypography
                    variant="h1"
                    fontSize={"24px"}
                    fontWeight={700}
                    lineHeight={1.2}
                    marginBottom={"50px"}
                  >
                    Type of recommendation
                  </MLTypography>
                  <RecommendationTypes
                    recommendationTypes={{
                      setup: organization.typeOfRecommendation
                        ? organization.typeOfRecommendation.setup
                        : 0,
                      products: organization.typeOfRecommendation
                        ? organization.typeOfRecommendation.product
                        : 0,
                      habits: organization.typeOfRecommendation
                        ? organization.typeOfRecommendation.habit
                        : 0,
                    }}
                  />
                </>
              )}
            </Stack>
          </Stack>

          <Stack
            direction={{ sm: "column", md: "row" }}
            marginTop={"50px"}
            gap={"50px"}
          >
            {/** Issue resolution compliance  */}
            <Stack gap={"30px"} flex={3}>
              <MLTypography
                variant="h1"
                fontSize={"24px"}
                fontWeight={700}
                lineHeight={1.2}
              >
                Issue resolution compliance
              </MLTypography>

              <IssueResolutionCompliance
                total={
                  completedSelfAssessments ? completedSelfAssessments.length : 0
                }
                highProgress={employeesResolutionCompliance.highProgress}
                mediumProgress={employeesResolutionCompliance.mediumProgress}
                needsAttention={employeesResolutionCompliance.needsAttention}
              />
            </Stack>

            {/** Detail Implementation Breakdown */}
            {/* <Stack gap={"30px"} flex={7}>
                <DetailImplementationBreakdown
                  total={0}
                  fully={0}
                  nearly={0}
                  good={0}
                  minimal={0}
                  started={0}
                  noAction={0}
                />
              </Stack> */}
          </Stack>
        </MLContainer>
      </Stack>
    </>
  );
};

export default ErgoAnalytics;
