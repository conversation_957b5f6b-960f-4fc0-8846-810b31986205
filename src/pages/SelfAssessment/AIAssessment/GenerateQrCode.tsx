import React, { useEffect, useState, useRef } from 'react';
import { Box, Stack, useMediaQuery, useTheme, Alert, <PERSON><PERSON>kbar, IconButton } from "@mui/material";
import { useCreate, useUpdate, useCustomMutation, BaseKey, useOne, useList } from "@refinedev/core";
import MLTypography from "../../../components/ui/MLTypography/MLTypography";
import Loading from '../../Loading/Loading';
import QRCode from './QRCode';
import { API_URL, TOKEN_KEY } from "../../../constants";
import { Close } from '@mui/icons-material';

interface QRCodeSectionProps {
    employeeId: string | number;
    isAnalyzing: boolean;
    employeeRole?: boolean;
    setShowQrCode: (show: boolean) => void;
}

// QR Code info needed for persistence
export interface StoredQRInfo {
    caseId: number;
    token?: string;
    url?: string;
    expiresAt?: number; // timestamp when QR expires - not used for temporary fix
}

// Setting a very long lifetime (1 year in seconds) as a temporary fix
const QR_LIFETIME = 31536000;
export const STORAGE_KEY = "qr_code_info"; // Key for localStorage

const QRCodeSection: React.FC<QRCodeSectionProps> = ({ employeeRole, employeeId, isAnalyzing, setShowQrCode }) => {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const [token, setToken] = useState<string>('');
    const [caseId, setCaseId] = useState<number | null>(null);
    const [qrUrl, setQrUrl] = useState<string>('');
    const [isGenerating, setIsGenerating] = useState<boolean>(false);
    // We'll still keep the timeLeft state but prevent it from counting down
    const [timeLeft, setTimeLeft] = useState<number>(QR_LIFETIME);
    const [isActive, setIsActive] = useState<boolean>(false);
    const [isExpired, setIsExpired] = useState<boolean>(false);
    const qrCodeRef = useRef<HTMLDivElement>(null);
    const generationAttempts = useRef<number>(0);
    const maxRetries = 3;
    const firstLoadComplete = useRef<boolean>(false);

    // Error handling states
    const [error, setError] = useState<string | null>(null);
    const [showError, setShowError] = useState<boolean>(false);

    // Use Refine hooks for API calls
    const { mutate: createAICase } = useCreate();
    const { mutate: mutateCreateCase } = useCreate();
    const { mutate: updateAICase } = useUpdate();
    const { mutate: customMutation } = useCustomMutation();

    // Use Refine's useList to fetch existing cases for the employee
    const { data: existingCasesData, isLoading: isLoadingCases } = useList({
        resource: "ai-cases",
        filters: [
            {
                field: "employee",
                operator: "eq",
                value: employeeId,
            }
        ],
        queryOptions: {
            enabled: !!employeeId, // Only run query if employeeId exists
        },
        sorters: [ // get the latest ai case
            {
                field: "updatedAt",
                order: "desc"
            }
        ],
        pagination: {
            pageSize: 1,
        },
    });

    // Check case status only once when needed, not using polling
    const { refetch: refetchCase } = useOne({
        resource: "ai-cases",
        id: caseId as BaseKey,
        queryOptions: {
            enabled: false, // Disable automatic fetching, we'll manually call refetch
        },
    });

    // Function to get the base URL from the current browser location
    const getBaseUrl = (): string => {
        if (process.env.NODE_ENV !== "production") {
            const localXposeDomains = [
                "rohitm.ap.loclx.io",
                "rakesh.ap.loclx.io",
                "alanyyk.ap.loclx.io",
            ]
            return localXposeDomains[0];
        } else {
            const protocol = window.location.protocol;
            const hostname = window.location.hostname;
            const port = window.location.port ? `:${window.location.port}` : '';
            return `${protocol}//${hostname}${port}`;
        }
    };

    // Generate a secure random token
    const generateToken = () => {
        const randomBytes = new Uint8Array(24);
        window.crypto.getRandomValues(randomBytes);
        return Array.from(randomBytes)
            .map(b => b.toString(16).padStart(2, '0'))
            .join('');
    };

    // Save QR info to localStorage for persistence
    const saveQRInfo = (info: StoredQRInfo) => {
        try {
            localStorage.setItem(STORAGE_KEY, JSON.stringify(info));
        } catch (err) {
            console.error("Error saving QR info to localStorage:", err);
        }
    };

    // Load QR info from localStorage
    const loadQRInfo = (): StoredQRInfo | null => {
        try {
            const storedInfo = localStorage.getItem(STORAGE_KEY);
            if (!storedInfo) return null;

            const parsedInfo = JSON.parse(storedInfo) as StoredQRInfo;

            // Make sure it's for the current employee and url exists
            if (parsedInfo.url && parsedInfo.url.includes(`/${employeeId}/`)) {
                return parsedInfo;
            }
            return null;
        } catch (err) {
            console.error("Error loading QR info from localStorage:", err);
            return null;
        }
    };

    // Clear stored QR info
    const clearQRInfo = () => {
        try {
            // localStorage.removeItem(STORAGE_KEY);
            saveQRInfo({
                caseId: caseId ?? 0,
            });
        } catch (err) {
            console.error("Error clearing QR info from localStorage:", err);
        }
    };

    // Invalidate QR code in the database - MODIFIED to always keep QR valid
    const invalidateQRCode = async () => {
        // Temporary fix: Comment out the invalidation logic
        // if (caseId) {
        //     try {
        //         updateAICase({
        //             resource: "ai-cases",
        //             id: caseId,
        //             values: {
        //                 qrValid: false
        //             }
        //         },
        //             {
        //                 onSuccess: () => {
        //                     console.log("QR code invalidated successfully");
        //                 },
        //                 onError: (error) => {
        //                     console.error("Error invalidating QR code:", error);
        //                 }
        //             });
        //     } catch (error) {
        //         console.error("Error invalidating QR code:", error);
        //     }
        // }
        console.log("QR code invalidation skipped due to temporary fix");
    };

    // Upload QR code image using canvas to capture the rendered component
    const uploadQrCodeImage = async (caseId: number) => {
        try {
            if (!qrCodeRef.current) return;

            const svgElement = qrCodeRef.current.querySelector('svg');
            if (!svgElement) return;

            // Create a canvas with proper dimensions
            const canvas = document.createElement('canvas');
            canvas.width = 160;
            canvas.height = 160;
            const ctx = canvas.getContext('2d');
            if (!ctx) return;

            // Create a temporary image
            const img = new Image();

            // Serialize SVG
            const svgData = new XMLSerializer().serializeToString(svgElement);
            const svgBlob = new Blob([svgData], { type: 'image/svg+xml' });
            const svgUrl = URL.createObjectURL(svgBlob);

            // Draw SVG on canvas
            img.onload = () => {
                ctx.drawImage(img, 0, 0, 160, 160);

                // Convert canvas to blob
                canvas.toBlob(async (blob) => {
                    if (!blob) return;

                    // Create form data for upload
                    const formData = new FormData();
                    formData.append('files', blob, `qrcode_${caseId}.png`);
                    formData.append('refId', caseId.toString());
                    formData.append('ref', 'api::ai-case.ai-case');
                    formData.append('field', 'qrcode');

                    // Upload using customMutation
                    customMutation(
                        {
                            url: `${API_URL}/api/upload`,
                            method: 'post',
                            values: formData,
                            successNotification: false
                        },
                        {
                            onSuccess: () => {
                                //
                            },
                            onError: (error) => {
                                console.error('Error uploading QR code image:', error);
                            }
                        }
                    );

                    // Revoke URL to prevent memory leaks
                    URL.revokeObjectURL(svgUrl);
                }, 'image/png');
            };

            // Set image source to SVG URL
            img.src = svgUrl;
        } catch (error) {
            console.error('Error processing QR code image:', error);
        }
    };

    // Create or update an AI case and generate QR code
    const handleGenerateQR = async () => {
        if (isGenerating || !employeeId || isLoadingCases) {
            return;
        }

        setIsExpired(false);
        setIsGenerating(true);
        setError(null);
        generationAttempts.current += 1;

        try {
            const newToken = generateToken();
            setToken(newToken);
            const baseUrl = getBaseUrl();

            // Get JWT token from localStorage
            const jwtToken = localStorage.getItem(TOKEN_KEY);
            if (!jwtToken) {
                setError("Authentication error. Please login again.");
                setShowError(true);
                setIsGenerating(false);
                return;
            }

            // Create a new AI case
            createAICase(
                {
                    resource: "ai-cases",
                    values: {
                        employee: employeeId,
                        qrValid: true,
                        token: newToken
                    },
                },
                {
                    onSuccess: (res: any) => {
                        const newAiCaseId = res.data.data.id;
                        !employeeRole && mutateCreateCase(
                            {
                                resource: "cases",
                                values: {
                                    aiCase: newAiCaseId,
                                    // Add any other required fields for case creation
                                    employee: employeeId,
                                    mode: "in_person",
                                    type: "general_assessment",
                                    // You might need additional fields like status, title, etc.
                                },
                            },
                            {
                                onSuccess: () => {
                                    console.log("[SUCCESS] created associated case");
                                }
                            }
                        );

                        setCaseId(newAiCaseId);

                        // Create dynamic QR URL with JWT token for authentication
                        const generatedUrl = `${baseUrl}/self-assessment/ai/${employeeId}/${newAiCaseId}/${newToken}?auth=${encodeURIComponent(jwtToken)}`;
                        setQrUrl(generatedUrl);

                        // Set a very distant expiration time (1 year from now) for temporary fix
                        const expiresAt = Math.floor(Date.now() / 1000) + QR_LIFETIME;

                        // Save to localStorage
                        saveQRInfo({
                            caseId: newAiCaseId,
                            token: newToken,
                        });

                        // Start timer but don't actually count down (temporary fix)
                        setTimeLeft(QR_LIFETIME);
                        setIsActive(true);
                        setIsGenerating(false);
                        generationAttempts.current = 0;

                        // Upload the QR code image after it renders
                        setTimeout(() => {
                            uploadQrCodeImage(newAiCaseId);
                        }, 500);
                    },
                    onError: (createError: any) => {
                        handleCreateError(createError);
                    }
                }
            );

            // Check if employee already has a case
            // const existingCases = existingCasesData?.data || [];
            // // const existingCase = existingCases.length > 0 ?  existingCases[0] : null;
            // let existingCase = null;

            // if (existingCases.length > 0) {
            //     const firstCase = existingCases[0];
            //     const bothCompleted =
            //         firstCase.chairDeskImageProgress === "Completed" &&
            //         firstCase.chairImageProgress === "Completed";

            //     const eitherFailed =
            //         firstCase.chairDeskImageProgress === "FailedProcessing" ||
            //         firstCase.chairImageProgress === "FailedProcessing";

            //     if (bothCompleted || eitherFailed) {
            //         existingCase = null;
            //     } else {
            //         existingCase = firstCase;
            //     }
            // }
            // const baseUrl = getBaseUrl();

            // if (existingCase) {
            //     // Reuse existing case - update with new token and set qrValid to true
            //     const existingCaseId = Number(existingCase.id);
            //     setCaseId(existingCaseId);

            //     updateAICase(
            //         {
            //             resource: "ai-cases",
            //             id: existingCaseId as BaseKey,
            //             values: {
            //                 token: newToken,
            //                 qrValid: true
            //             },
            //             successNotification: false
            //         },
            //         {
            //             onSuccess: () => {
            //                 // Create dynamic QR URL with JWT token for authentication
            //                 const generatedUrl = `${baseUrl}/self-assessment/ai/${employeeId}/${existingCaseId}/${newToken}?auth=${encodeURIComponent(jwtToken)}`;
            //                 // console.log('generatedUrl: ', generatedUrl);
            //                 setQrUrl(generatedUrl);

            //                 // Set a very distant expiration time (1 year from now) for temporary fix
            //                 const expiresAt = Math.floor(Date.now() / 1000) + QR_LIFETIME;

            //                 // Save to localStorage
            //                 saveQRInfo({
            //                     caseId: existingCaseId,
            //                     token: newToken,
            //                 });

            //                 // Start timer but don't actually count down (temporary fix)
            //                 setTimeLeft(QR_LIFETIME);
            //                 setIsActive(true);
            //                 setIsGenerating(false);
            //                 generationAttempts.current = 0;

            //                 // Upload the QR code image after it renders
            //                 setTimeout(() => {
            //                     uploadQrCodeImage(existingCaseId);
            //                 }, 500);
            //             },
            //             onError: (updateError: any) => {
            //                 handleUpdateError(updateError);
            //             }
            //         }
            //     );
            //     !employeeRole && mutateCreateCase(
            //         {
            //             resource: "cases",
            //             values: {
            //                 aiCase: existingCaseId,
            //                 // Add any other required fields for case creation
            //                 employee: employeeId,
            //                 mode: "in_person",
            //                 type: "general_assessment",
            //                 // You might need additional fields like status, title, etc.
            //             },
            //         },
            //         {
            //             onSuccess: () => {
            //                 // console.log("[SUCCESS] created associated case");
            //             }
            //         }
            //     );
            // } else {
            //     // Create a new AI case
            //     createAICase(
            //         {
            //             resource: "ai-cases",
            //             values: {
            //                 employee: employeeId,
            //                 qrValid: true,
            //                 token: newToken
            //             },
            //         },
            //         {
            //             onSuccess: (res: any) => {
            //                 const newAiCaseId = res.data.data.id;
            //                 !employeeRole && mutateCreateCase(
            //                     {
            //                         resource: "cases",
            //                         values: {
            //                             aiCase: newAiCaseId,
            //                             // Add any other required fields for case creation
            //                             employee: employeeId,
            //                             mode: "in_person",
            //                             type: "general_assessment",
            //                             // You might need additional fields like status, title, etc.
            //                         },
            //                     },
            //                     {
            //                         onSuccess: () => {
            //                             console.log("[SUCCESS] created associated case");
            //                         }
            //                     }
            //                 );

            //                 setCaseId(newAiCaseId);

            //                 // Create dynamic QR URL with JWT token for authentication
            //                 const generatedUrl = `${baseUrl}/self-assessment/ai/${employeeId}/${newAiCaseId}/${newToken}?auth=${encodeURIComponent(jwtToken)}`;
            //                 setQrUrl(generatedUrl);

            //                 // Set a very distant expiration time (1 year from now) for temporary fix
            //                 const expiresAt = Math.floor(Date.now() / 1000) + QR_LIFETIME;

            //                 // Save to localStorage
            //                 saveQRInfo({
            //                     caseId: newAiCaseId,
            //                     token: newToken,
            //                 });

            //                 // Start timer but don't actually count down (temporary fix)
            //                 setTimeLeft(QR_LIFETIME);
            //                 setIsActive(true);
            //                 setIsGenerating(false);
            //                 generationAttempts.current = 0;

            //                 // Upload the QR code image after it renders
            //                 setTimeout(() => {
            //                     uploadQrCodeImage(newAiCaseId);
            //                 }, 500);
            //             },
            //             onError: (createError: any) => {
            //                 handleCreateError(createError);
            //             }
            //         }
            //     );
            // }
        } catch (error: any) {
            console.error("Unexpected error in QR code generation:", error);
            setError("Unexpected error. Please try again.");
            setShowError(true);
            setIsGenerating(false);
            retryIfNeeded();
        }
    };

    // calls handleGenerateQR on page load
    useEffect(() => {
        if (!isLoadingCases) {
            handleGenerateQR();
        }
    }, [isLoadingCases])

    // Helper functions for error handling
    const handleUpdateError = (updateError: any) => {
        if (updateError?.status === 403) {
            setError("Permission denied. You don't have access to update this resource.");
        } else {
            setError(`Failed to generate QR code: ${updateError?.message || 'Token update error'}`);
        }
        setShowError(true);
        setIsGenerating(false);
        retryIfNeeded();
    };

    const handleCreateError = (createError: any) => {
        if (createError?.status === 403) {
            setError("Permission denied. You don't have access to create this resource.");
        } else if (createError?.status === 429) {
            setError("Too many requests. Please try again in a moment.");
        } else if (createError?.status >= 500) {
            setError("Server error. Please try again later.");
        } else {
            setError(`Failed to generate QR code: ${createError?.message || 'Unknown error'}`);
        }
        setShowError(true);
        setIsGenerating(false);
        retryIfNeeded();
    };

    const retryIfNeeded = () => {
        if (generationAttempts.current < maxRetries) {
            setTimeout(() => {
                console.log(`Retrying QR generation, attempt ${generationAttempts.current + 1}/${maxRetries}`);
                handleGenerateQR();
            }, 3000); // 3-second delay between retries
        } else {
            console.error(`Max retry attempts (${maxRetries}) reached for QR generation`);
        }
    };

    // Check if stored QR is still valid - MODIFIED to always return true
    const isStoredQRValid = (storedInfo: StoredQRInfo): boolean => {
        // Temporary fix: Always return true
        return true;

        // Original code:
        // const currentTime = Math.floor(Date.now() / 1000);
        // return !!storedInfo.expiresAt && storedInfo.expiresAt > currentTime;
    };

    // Calculate remaining time for stored QR - MODIFIED to always return maximum time
    const calculateRemainingTime = (storedInfo: StoredQRInfo): number => {
        // Temporary fix: Always return QR_LIFETIME
        return QR_LIFETIME;

        // Original code:
        // const currentTime = Math.floor(Date.now() / 1000);
        // const expirationTime = storedInfo.expiresAt || 0;
        // const remaining = expirationTime - currentTime;
        // return remaining > 0 ? remaining : 0;
    };

    // Manually check case status only when needed
    const checkCaseStatus = async () => {
        // Temporary fix: Skip case status check
        return;

        // Original code:
        // if (caseId && isActive) {
        //     const result = await refetchCase();
        //     if (result.data?.data) {
        //         const caseQrValid = result.data.data.qrValid;
        //         if (caseQrValid === false && isActive) {
        //             setIsActive(false);
        //             setIsExpired(true);
        //             clearQRInfo();
        //         }
        //     }
        // }
    };

    // Set up regular interval to check case status without continuous polling
    useEffect(() => {
        // Temporary fix: Skip setting up the interval
        return () => { };

        // Original code:
        // if (isActive && caseId) {
        //     // Check case status every 15 seconds instead of continuous polling
        //     const checkInterval = setInterval(() => {
        //         checkCaseStatus();
        //     }, 15000);
        //
        //     return () => clearInterval(checkInterval);
        // }
    }, [isActive, caseId]);

    // Restore QR state from localStorage if available
    const restoreQRState = () => {
        if (firstLoadComplete.current) return;

        const storedInfo = loadQRInfo();
        if (storedInfo && isStoredQRValid(storedInfo)) {
            // QR code is still valid, restore state
            setCaseId(storedInfo.caseId);
            if (storedInfo.token) {
                setToken(storedInfo.token);
            }
            if (storedInfo.url) {
                setQrUrl(storedInfo.url);
            }
            setTimeLeft(calculateRemainingTime(storedInfo));
            setIsActive(true);
            setIsExpired(false);
            setIsGenerating(false);

            // Check case status once when restoring
            setTimeout(() => {
                checkCaseStatus();
            }, 500);
        } else if (storedInfo) {
            // QR code exists but is expired - MODIFIED to handle as if not expired
            setCaseId(storedInfo.caseId);
            if (storedInfo.token) {
                setToken(storedInfo.token);
            }
            if (storedInfo.url) {
                setQrUrl(storedInfo.url);
            }

            // Temporary fix: Always set as active
            setTimeLeft(QR_LIFETIME);
            setIsActive(true);
            setIsExpired(false);
            setIsGenerating(false);

            // Original code:
            // setTimeLeft(0);
            // setIsActive(false);
            // setIsExpired(true);
            // setIsGenerating(false);
            // 
            // // Ensure we set qrValid to false in the database
            // invalidateQRCode();
        } else {
            // No stored QR, generate a new one
            setIsGenerating(false);
            setTimeout(() => {
                handleGenerateQR();
            }, 100);
        }

        firstLoadComplete.current = true;
    };

    // Timer countdown effect - MODIFIED to prevent countdown
    useEffect(() => {
        // Temporary fix: Skip timer countdown
        return () => { };

        // Original code:
        // let interval: NodeJS.Timeout | null = null;
        // if (isActive && timeLeft > 0) {
        //     interval = setInterval(() => {
        //         setTimeLeft(prevTime => {
        //             const newTime = prevTime - 1;
        //
        //             // Update expiration timestamp in storage
        //             if (caseId && qrUrl) {
        //                 const expiresAt = Math.floor(Date.now() / 1000) + newTime;
        //                 saveQRInfo({
        //                     caseId: caseId,
        //                     token: token,
        //                     url: qrUrl,
        //                     expiresAt
        //                 });
        //             }
        //
        //             return newTime;
        //         });
        //     }, 1000);
        // } else if (timeLeft === 0 && isActive) {
        //     setIsActive(false);
        //     setIsExpired(true);
        //     invalidateQRCode();
        //     clearQRInfo();
        // }
        //
        // return () => {
        //     if (interval) clearInterval(interval);
        // };
    }, [isActive, timeLeft, caseId, qrUrl, token]);

    // Initial setup on component load
    useEffect(() => {
        if (!isLoadingCases && employeeId && !isAnalyzing) {
            setTimeout(() => {
                restoreQRState();
            }, 300);
        }
    }, [isLoadingCases, employeeId, isAnalyzing]);

    // Add a failsafe timer to ensure we don't get stuck in generating state
    useEffect(() => {
        if (isGenerating && !qrUrl) {
            const failsafeTimer = setTimeout(() => {
                setIsGenerating(false);
                setTimeout(() => {
                    handleGenerateQR();
                }, 500);
            }, 5000); // 5 second failsafe

            return () => clearTimeout(failsafeTimer);
        }
    }, [isGenerating, qrUrl]);

    // Modified to display "non-expiring" instead of countdown
    const formatTime = (seconds: number) => {
        // Temporary fix: Return a message that indicates QR won't expire
        return "non-expiring code";

        // Original code:
        // const mins = Math.floor(seconds / 60);
        // const secs = seconds % 60;
        //
        // if (seconds < 60) {
        //     // Less than a minute, show only seconds
        //     return `${seconds} seconds`;
        // } else {
        //     // More than a minute, show minutes:seconds format
        //     return `${mins}:${secs.toString().padStart(2, '0')} minutes`;
        // }
    };

    // Handle error snackbar close
    const handleCloseError = () => {
        setShowError(false);
    };

    // Try again after error
    const handleTryAgain = () => {
        setShowError(false);
        setError(null);
        generationAttempts.current = 0;
        setTimeout(() => {
            handleGenerateQR();
        }, 300);
    };

    // Manual regenerate option
    const handleManualRegenerate = () => {
        if (!isGenerating) {
            setQrUrl('');
            setToken('');
            setIsActive(false);
            setIsExpired(false);
            generationAttempts.current = 0;
            clearQRInfo();
            setTimeout(() => {
                handleGenerateQR();
            }, 300);
        }
    };

    // Don't render component on mobile devices
    if (isMobile) {
        return null;
    }

    // Show loading while fetching existing cases
    if (isLoadingCases) {
        return (
            <Stack width="100%" height="200px" display="flex" alignItems="center" justifyContent="center" m="0 auto">
                <Loading />
                <MLTypography variant="body2" mt={1}>
                    Loading...
                </MLTypography>
            </Stack>
        );
    }

    return (
        <Box
            sx={{
                bgcolor: "#FFE2E2",
                borderRadius: "8px",
                p: "20px",
                display: "flex",
                flexDirection: { xs: "column", sm: "row" },
                alignItems: "center",
                gap: '26px',
                width: "100%",
                mb: 2.5
            }}
        >
            {/* Left side - QR Code component */}
            <Box
                sx={{
                    // width: { xs: "100%", sm: "150px" },
                    // height: { xs: "auto", sm: "150px" },
                    width: "auto",
                    height: "100%",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center"
                }}
            >
                <QRCode
                    url={qrUrl}
                    isGenerating={isGenerating}
                    isExpired={isExpired}
                    error={error}
                    onTryAgain={handleTryAgain}
                    onRegenerate={handleManualRegenerate}
                    size={100}
                    qrCodeRef={qrCodeRef}
                />
            </Box>

            {/* Right side - Text content */}
            <Stack
                sx={{
                    flex: 1,
                    justifyContent: "space-between",
                }}
                direction={"row"}
                gap={"26px"}
            >
                <Stack>
                    <MLTypography variant="h2" fontSize={"24px"} fontWeight={700} mb={'10px'}>
                        Upload photos with your phone
                    </MLTypography>

                    <Stack>
                        <Stack direction="row" spacing={1} alignItems="flex-start">
                            <Box component="span" sx={{ fontWeight: 700, display: "inline-block", mt: "2px" }}>•</Box>
                            <MLTypography fontSize={"14px"} variant="body1" fontWeight={400} lineHeight={1.5}>
                                Scan this code with your phone
                            </MLTypography>
                        </Stack>
                        <Stack direction="row" spacing={1} alignItems="flex-start">
                            <Box component="span" sx={{ fontWeight: 700, display: "inline-block", mt: "2px" }}>•</Box>
                            <MLTypography fontSize={"14px"} variant="body1" fontWeight={400} lineHeight={1.5}>
                                Choose which photos to take
                            </MLTypography>
                        </Stack>
                    </Stack>

                    {/* {qrUrl && !isExpired && (
                    <Stack direction="row" spacing={1} alignItems="center" mt={1}>
                        <AccessTimeIcon fontSize="small" color="action" />
                        <MLTypography
                            variant="body2"
                            fontSize="14px"
                            fontWeight={500}
                        >
                            {formatTime(timeLeft)}
                        </MLTypography>
                    </Stack>
                )} */}

                    {isExpired && (
                        <MLTypography
                            variant="body2"
                            fontSize="14px"
                            fontWeight={500}
                            color="error"
                            mt={1}
                        >
                            QR code has expired. Please generate a new one.
                        </MLTypography>
                    )}
                </Stack>
                <Stack>
                    <IconButton
                        disableRipple
                        size="small"
                        onClick={() => setShowQrCode(false)}
                        sx={{
                            padding: 0
                        }}
                    >
                        <Close />
                    </IconButton>
                </Stack>
            </Stack>

            {/* Error message snackbar */}
            <Snackbar
                open={showError}
                autoHideDuration={6000}
                onClose={handleCloseError}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
            >
                <Alert onClose={handleCloseError} severity="error" variant="filled" sx={{ width: '100%' }}>
                    {error}
                </Alert>
            </Snackbar>
        </Box>
    );
};

export default QRCodeSection;