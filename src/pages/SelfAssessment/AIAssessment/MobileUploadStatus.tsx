import React, { useEffect, useState } from 'react';
import { Box, Stack, LinearProgress, CircularProgress } from '@mui/material';
import MLTypography from '../../../components/ui/MLTypography/MLTypography';
import MLButton from '../../../components/ui/MLButton/MLButton';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import PhotoLibraryIcon from '@mui/icons-material/PhotoLibrary';
import SyncIcon from '@mui/icons-material/Sync';

interface MobileUploadStatusProps {
    status: 'uploading' | 'receiving' | 'processing' | 'complete';
    onClose?: () => void;
    progress?: number;
}

/**
 * Overlay component that displays the status of photo upload from mobile to desktop
 */
const MobileUploadStatus: React.FC<MobileUploadStatusProps> = ({
    status,
    onClose,
    progress = 0
}) => {
    const [progressValue, setProgressValue] = useState(progress);

    // Simulate progress if we don't have actual progress data
    useEffect(() => {
        if (status === 'uploading' && progress === 0) {
            const timer = setInterval(() => {
                setProgressValue((prevProgress) => {
                    // Cap at 90% for uploading to show we're still waiting
                    if (prevProgress >= 90) {
                        clearInterval(timer);
                        return 90;
                    }
                    return prevProgress + 10;
                });
            }, 1000);
            return () => {
                clearInterval(timer);
            };
        } else {
            setProgressValue(progress);
        }
    }, [status, progress]);

    return (
        <Box
            sx={{
                position: 'fixed',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 9999,
            }}
        >
            <Stack
                sx={{
                    maxWidth: 450,
                    width: '90%',
                    padding: 4,
                    borderRadius: 2,
                    backgroundColor: '#ffffff',
                    textAlign: 'center'
                }}
                spacing={3}
            >
                {status === 'uploading' && (
                    <>
                        <CloudUploadIcon sx={{ fontSize: 60, color: '#7856FF', alignSelf: 'center' }} />
                        <MLTypography fontFamily={'work sans'} variant="h5" gutterBottom fontWeight={600}>
                            Uploading photos to server...
                        </MLTypography>
                        <LinearProgress
                            variant="determinate"
                            value={progressValue}
                            sx={{ height: 8, borderRadius: 4 }}
                        />
                        <MLTypography fontFamily={'work sans'} variant="body2">
                            Please keep this page open while we upload your photos.
                        </MLTypography>
                    </>
                )}

                {status === 'receiving' && (
                    <>
                        <SyncIcon sx={{ fontSize: 60, color: '#7856FF', alignSelf: 'center' }} />
                        <MLTypography fontFamily={'work sans'} variant="h5" gutterBottom fontWeight={600}>
                            Syncing with desktop...
                        </MLTypography>
                        <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                            <CircularProgress size={40} />
                        </Box>
                        <MLTypography fontFamily={'work sans'} variant="body2">
                            Your photos are being synchronized with the desktop application.
                        </MLTypography>
                    </>
                )}

                {status === 'processing' && (
                    <>
                        <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                            <CircularProgress size={60} color="secondary" />
                        </Box>
                        <MLTypography variant="h5" gutterBottom fontWeight={600}>
                            Starting AI Analysis
                        </MLTypography>
                        <MLTypography variant="body1">
                            Your photos are being analyzed on the desktop.
                            You may close this window now.
                        </MLTypography>
                        {onClose && (
                            <MLButton
                                variant="contained"
                                color="primary"
                                onClick={onClose}
                                sx={{ mt: 2 }}
                            >
                                Close
                            </MLButton>
                        )}
                    </>
                )}

                {status === 'complete' && (
                    <>
                        <CheckCircleIcon sx={{ fontSize: 60, color: '#4CAF50', alignSelf: 'center' }} />
                        <MLTypography variant="h5" gutterBottom fontWeight={600}>
                            Photos sent successfully!
                        </MLTypography>
                        <MLTypography variant="body1">
                            You can close this window and continue on the desktop.
                        </MLTypography>
                        {onClose && (
                            <MLButton
                                variant="contained"
                                color="primary"
                                onClick={onClose}
                                sx={{ mt: 2 }}
                            >
                                Close
                            </MLButton>
                        )}
                    </>
                )}
            </Stack>
        </Box>
    );
};

export default MobileUploadStatus;


// import React from 'react';
// import { Box, Stack, LinearProgress } from '@mui/material';
// import MLTypography from '../../../components/ui/MLTypography/MLTypography';
// import MLButton from '../../../components/ui/MLButton/MLButton';
// import CloudUploadIcon from '@mui/icons-material/CloudUpload';
// import CheckCircleIcon from '@mui/icons-material/CheckCircle';

// interface MobileUploadStatusProps {
//     status: 'uploading' | 'complete';
//     onClose?: () => void;
// }

// /**
//  * Overlay component that displays the status of photo upload from mobile to desktop
//  */
// const MobileUploadStatus: React.FC<MobileUploadStatusProps> = ({ status, onClose }) => {
//     return (
//         <Box
//             sx={{
//                 position: 'fixed',
//                 top: 0,
//                 left: 0,
//                 right: 0,
//                 bottom: 0,
//                 backgroundColor: 'rgba(0, 0, 0, 0.7)',
//                 display: 'flex',
//                 alignItems: 'center',
//                 justifyContent: 'center',
//                 zIndex: 9999,
//             }}
//         >
//             <Stack
//                 sx={{
//                     maxWidth: 450,
//                     width: '90%',
//                     padding: 4,
//                     borderRadius: 2,
//                     backgroundColor: '#ffffff',
//                     textAlign: 'center'
//                 }}
//                 spacing={2}
//             >
//                 {status === 'uploading' ? (
//                     <>
//                         <CloudUploadIcon sx={{ fontSize: 60, color: '#7856FF', alignSelf: 'center' }} />
//                         <MLTypography fontFamily={'work sans'} variant="h5" gutterBottom fontWeight={600}>
//                             Sending photos to the computer...
//                         </MLTypography>
//                         <LinearProgress />
//                         <MLTypography fontFamily={'work sans'} variant="body2">
//                             Please keep this page open until the upload completes.
//                         </MLTypography>
//                     </>
//                 ) : (
//                     <>
//                         <CheckCircleIcon sx={{ fontSize: 60, color: '#4CAF50', alignSelf: 'center' }} />
//                         <MLTypography variant="h5" gutterBottom fontWeight={600}>
//                             Photos sent successfully!
//                         </MLTypography>
//                         <MLTypography variant="body1">
//                             You can close this window and continue on the desktop.
//                         </MLTypography>
//                         {onClose && (
//                             <MLButton
//                                 variant="contained"
//                                 color="primary"
//                                 onClick={onClose}
//                             >
//                                 Close
//                             </MLButton>
//                         )}
//                     </>
//                 )}
//             </Stack>
//         </Box>
//     );
// };

// export default MobileUploadStatus;