import { Box, Stack } from "@mui/material";
import MLTypography from "../../../../components/ui/MLTypography/MLTypography";
import MLButton from "../../../../components/ui/MLButton/MLButton";
import { ImgType } from "../AIAssessment";
import Loading from "../../../Loading/Loading";
import { useState } from "react";
import heic2any from 'heic2any';
import { useCustomMutation, useUpdate } from "@refinedev/core";
import { AI_API_BASE_URL, API_URL, CHAIR_ANALYSIS_ENDPOINT, DESK_ANALYSIS_ENDPOINT, STANDING_DESK_ANALYSIS_ENDPOINT } from "../../../../constants";
import axios from 'axios';

interface UploadPhotoProps {
  inputRef: React.RefObject<HTMLInputElement>;
  imgType: ImgType;
  imgUrl: string | undefined;
  imageFile: File | undefined;
  imgErrMsg: string;
  samplePhoto: string;
  setImageErrMsg: (msg: string) => void;
  setImage: (file: File) => void;
  token: string | undefined;
  aiCaseId: number | undefined;
  setIsUploadSuccess: (isSuccess: boolean) => void;
}
const UploadPhoto = ({
  inputRef,
  imgType,
  imgUrl,
  imageFile,
  imgErrMsg,
  samplePhoto,
  setImageErrMsg,
  setImage,
  token,
  aiCaseId,
  setIsUploadSuccess,
}: UploadPhotoProps) => {
  const { mutate: mutateCustomUploadAICaseImage } = useCustomMutation();
  const { mutate: mutateUpdateAICase } = useUpdate();

  const [isConverting, setIsConverting] = useState<boolean>(false);

  const handleUploadPhoto = async (photoFile: File | undefined, aiCaseId: number | undefined, photoType: ImgType) => {
    if (!photoFile) return;
    if (!aiCaseId) return;

    const ergonomicAnalyzeFormData = new FormData();
    ergonomicAnalyzeFormData.append("image", photoFile);
    ergonomicAnalyzeFormData.append("token", token ?? "");

    let apiEndpoint;
    switch (photoType) {
      case "chair_only":
        apiEndpoint = CHAIR_ANALYSIS_ENDPOINT;
        break;
      case "desk_chair":
        apiEndpoint = DESK_ANALYSIS_ENDPOINT;
        break;
      case "standing_desk":
        apiEndpoint = STANDING_DESK_ANALYSIS_ENDPOINT;
        break;
    }

    let analysisIdField;
    switch (photoType) {
      case "chair_only":
        analysisIdField = "chairImageAnalysisId";
        break;
      case "desk_chair":
        analysisIdField = "chairDeskImageAnalysisId";
        break;
      case "standing_desk":
        analysisIdField = "standingDeskImageAnalysisId";
        break;
    }

    let analyzeRes: any;
    try {
      analyzeRes = await axios.post(`${AI_API_BASE_URL}${apiEndpoint}`, ergonomicAnalyzeFormData);
      setIsUploadSuccess(analyzeRes.status === 200 ? true : false)
      console.log("analyzeRes = ", analyzeRes);
    } catch (e) {
      console.log("analyze upload error = ", e);
      setIsUploadSuccess(false)
    }

    mutateUpdateAICase(
      {
        resource: "ai-cases",
        values: {
          [analysisIdField]: analyzeRes.data.analysis_id
        },
        id: aiCaseId as number,
      },
      {
        onSuccess: async (res) => {
          console.log("[SUCCESS] update ai case: ", res.data.data);
        },
        onError: (error) => {
          console.log("[ERROR] create ai case: ", error)
        },
      },
    );

    let fieldPhotoType;
    switch (photoType) {
      case "chair_only":
        fieldPhotoType = "chairImage";
        break;
      case "desk_chair":
        fieldPhotoType = "chairDeskImage";
        break;
      case "standing_desk":
        fieldPhotoType = "standingImage";
        break;
    }

    const aiCaseUploadFormData = new FormData();
    aiCaseUploadFormData.append("files", photoFile, `aiCase_${aiCaseId}_${photoType}_image`);
    aiCaseUploadFormData.append("refId", aiCaseId.toString());
    aiCaseUploadFormData.append("ref", "api::ai-case.ai-case");
    aiCaseUploadFormData.append("field", fieldPhotoType);

    mutateCustomUploadAICaseImage(
      {
        url: `${API_URL}/api/upload`,
        method: "post",
        values: aiCaseUploadFormData,
      },
      {
        onSuccess: (res) => {
          console.log("[SUCCESS] upload photo to AI Case: ", res);
        },
        onError: (err) => {
          console.log("[ERROR] upload photo to AI Case: ", err);
        },
      }
    )
  }

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>, imgType: ImgType) => {
    const file = event.target.files;
    if (!file || file.length === 0) return;

    let imageFile = file[0];
    if (imageFile.size > 4900000) return setImageErrMsg("Image size must not be larger than 5 MB");

    // Convert HEIC to JPEG using heic2any
    if (imageFile.type === 'image/heic' || imageFile.type === 'image/heif' || imageFile.name.toLowerCase().endsWith('.heic') || imageFile.name.toLowerCase().endsWith('.heif')) {
      setIsConverting(true);

      const jpegBlob = await heic2any({
        blob: imageFile,
        toType: 'image/jpeg',
        quality: 0.8
      });

      const singleJpegBlob = Array.isArray(jpegBlob) ? jpegBlob[0] : jpegBlob;

      const jpegFile = new File(
        [singleJpegBlob],
        imageFile.name.replace(/\.(heic|heif)$/i, '.jpg'),
        { type: 'image/jpeg' }
      );

      imageFile = jpegFile;
      setIsConverting(false);
    }

    // check image resolution
    const url = URL.createObjectURL(imageFile);
    const img = new Image();
    let errorMsg = "";

    const isValidResolution = await new Promise((resolve) => {
      img.onload = () => {
        URL.revokeObjectURL(url);
        if (img.width < 512 || img.height < 512) {
          errorMsg = "Photo dimension must not be smaller than 512 x 512"
          return resolve(false);
        }

        const aspectRatio = img.width / img.height;

        if (!(aspectRatio >= 0.5 && aspectRatio <= 2)) {
          errorMsg = "Aspect ratio of photo must not be larger than 2 or lesser than 0.5";
          return resolve(false);
        }
        resolve(aspectRatio >= 0.5 && aspectRatio <= 2);
      };
      img.src = url;
    });

    // clear the file input value after setting file
    if (inputRef.current) {
      inputRef.current.value = '';
    }

    if (isValidResolution) {
      setImageErrMsg("");
      handleUploadPhoto(imageFile, aiCaseId, imgType);
      return setImage(imageFile);
    }

    setImageErrMsg(errorMsg);
  };

  const getHeaderText = (imgType: ImgType) => {
    switch (imgType) {
      case "chair_only":
        return "1. Sitting in chair";
      case "desk_chair":
        return "2. Working on computer";
      case "standing_desk":
        return "3. Standing (optional)";
    }
  }

  const getHelperText = (imgType: ImgType): string[] => {
    switch (imgType) {
      case "chair_only":
        return ["Full Side profile (Head to feet)", "Hands on thighs. Looking in front."];
      case "desk_chair":
        return ["Full Side profile (Head to feet)", "Hands on keyboard. Looking at screen"];
      case "standing_desk":
        return ["Full Side profile (Head to feet)", "Standing. Hands on keyboard. Looking at screen"];
      default:
        return [];
    }
  }

  return (
    <Stack direction={"column"} gap={"24px"}
      sx={{
        border: "0.5px solid #9C9C9C",
        borderRadius: "10px",
        padding: { xs: "20px", sm: "30px" },
        alignItems: { xs: "center", sm: "stretch" },
        height: "100%", // Make the entire stack take full height
        display: "flex",
        flexDirection: "column",
        width: "100%",
      }}
    >
      <Stack
        sx={{
          position: 'relative',
          width: "100%",
          alignItems: { xs: "center", sm: "flex-start" },
        }}
      >
        <MLTypography
          fontSize={"24px"}
          variant="h1"
          fontWeight={700}
          marginBottom={"20px"}
        >
          {getHeaderText(imgType)}
        </MLTypography>
        <Stack
          sx={{
            position: 'relative',
            alignItems: "center",
            width: "100%",
            height: "380px",
            backgroundColor: "#fcfcfc",
            overflow: "hidden",
          }}
        >
          {isConverting ? (
            <Loading />
          ) : (
            <Box
              component="img"
              sx={{
                border: "0.5px solid #9C9C9C",
                borderRadius: "10px",
                backgroundColor: "#fcfcfc",
                width: "100%",
                height: "380px",
                objectFit: imgUrl ? "contain" : "cover"
              }}
              src={imgUrl ? imgUrl : samplePhoto}
              alt="Sample photo"
            />
          )}
        </Stack>
        {imgUrl ? (
          <Box
            sx={{
              position: 'absolute',
              bottom: "4%",
              right: "5%",
              borderRadius: "5px",
              backgroundColor: "#31C100"
            }}
          >
            <MLTypography
              sx={{
                color: '#FFFFFF',
                padding: '5px 10px',
                fontWeight: 'bold',
                fontSize: '14px',
                userSelect: 'none',
                pointerEvents: 'none',
                textTransform: 'uppercase',
                letterSpacing: '1px',
                borderRadius: '4px',
              }}
            >
              ✔ Uploaded
            </MLTypography>
          </Box>
        ) : (
          <Box
            sx={{
              position: 'absolute',
              bottom: "4%",
              right: "5%",
              borderRadius: "5px",
              backgroundColor: "#FF6E6E"
            }}
          >
            <MLTypography
              sx={{
                color: '#FFFFFF',
                padding: '5px 10px',
                fontWeight: 'bold',
                fontSize: '14px',
                userSelect: 'none',
                pointerEvents: 'none',
                textTransform: 'uppercase',
                letterSpacing: '1px',
                borderRadius: '4px',
              }}
            >
              Sample Photo
            </MLTypography>
          </Box>
        )}
      </Stack>
      <Stack
        sx={{
          justifyContent: "space-between",
          flexGrow: 1, // Allow this section to grow and fill available space
          display: "flex",
          flexDirection: "column",
        }}
      >
        <Stack gap={"10px"} sx={{ minHeight: "60px", mb: "12px" }}>
          <ul style={{ paddingLeft: "18px", marginBlock: 0, gap: "0px" }}>
            {getHelperText(imgType).map((itemPoint: string, key) => (
              <li key={key + itemPoint}>
                <MLTypography fontSize={"16px"} variant="body1" fontWeight={400}>
                  {itemPoint}
                </MLTypography>
              </li>
            ))}
          </ul>
        </Stack>
        <Stack width={"100%"} sx={{ marginTop: "auto", marginBottom: 0 }}> {/* Push to bottom */}
          <input
            type="file"
            ref={inputRef}
            onChange={(e) => handleFileChange(e, imgType)}
            style={{ display: 'none' }}
            accept="image/*"
          />
          {/* Error message - only renders when there's an error */}
          {imgErrMsg ? (
            <MLTypography fontSize={"16px"} variant="body1" fontWeight={400} color={"red"} sx={{ mb: "15px" }}>
              {imgErrMsg}
            </MLTypography>
          ) : null}

          <MLButton
            fullWidth
            variant="outlined"
            color="primary"
            sx={{
              textTransform: 'uppercase',
              alignSelf: 'flex-start',
            }}
            onClick={() => inputRef.current?.click()}
          >
            {imgUrl ? "Reupload" : "Browse to attach"}
          </MLButton>
        </Stack>
      </Stack>
    </Stack>
  );
}
export default UploadPhoto;