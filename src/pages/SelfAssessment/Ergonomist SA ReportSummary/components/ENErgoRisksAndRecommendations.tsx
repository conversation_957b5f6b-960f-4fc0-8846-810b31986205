import { <PERSON>, Stack, IconButton, Skeleton, Divider } from '@mui/material'
import MLTypography from '../../../../components/ui/MLTypography/MLTypography'
import MLButton from '../../../../components/ui/MLButton/MLButton';
import MLInputbox from '../../../../components/ui/MLInputbox/MLInputbox';
import cross from "../../../../assets/reportIcons/cross.png";
import tick from "../../../../assets/reportIcons/tick.png";
import { useCustomMutation } from '@refinedev/core';
import { IActionPlan } from '../../Report/Report';
import AskAQuestion from '../../../Dashboard/AskAQuestion';
import { createRef, useEffect, useState } from 'react';
import CustomRightArrow from '../../../../assets/icons/CustomRightArrow';
import { Edit, Close, Save } from "@mui/icons-material";
import { ErCamera } from "@mindlens/ergo-icons";
import { API_URL } from '../../../../constants';

interface ENErgoRisksAndRecommendationsProps {
    actionPlans: IActionPlan[];
    handleActionPlanComplete: (outcome: string, points: number, isCompleted?: boolean) => void,
    handleViewAll?: () => void
    handleEditActionPlan: (updatedActionPlan: IActionPlan) => void
}

const ENErgoRisksAndRecommendations: React.FC<ENErgoRisksAndRecommendationsProps> = ({ actionPlans, handleActionPlanComplete, handleEditActionPlan, handleViewAll }) => {
    const [isNeedHelpModalOpened, setIsNeedHelpModalOpened] = useState<boolean>(false);
    const [editingIndex, setEditingIndex] = useState<number | null>(null);
    const [editedTitle, setEditedTitle] = useState<string>("");
    const [editedRiskText, setEditedRiskText] = useState<string>("");
    const [editedRiskTitle, setEditedRiskTitle] = useState<string>("");
    const [isLeftImageLoading, setIsLeftImageLoading] = useState<boolean>(false);
    const [isRightImageLoading, setIsRightImageLoading] = useState<boolean>(false);
    const leftFileInputRefs = Array(3).fill(0).map(() => createRef<HTMLInputElement>());
    const rightFileInputRefs = Array(3).fill(0).map(() => createRef<HTMLInputElement>());
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState<boolean>(false);
    const [leftImageError, setLeftImageError] = useState<string | null>(null);
    const [rightImageError, setRightImageError] = useState<string | null>(null);

    const { mutate: mutateCustom } = useCustomMutation();

    useEffect(() => {
        if (editingIndex !== null) {
            const action = actionPlans[editingIndex];
            const optionConfig = action.optionConfig || {};

            const titleChanged = editedTitle !== action.title;
            const riskTextChanged = editedRiskText !== optionConfig.overallRisk;
            const riskTitleChanged = editedRiskTitle !== optionConfig.riskTitle;

            setHasUnsavedChanges(titleChanged || riskTextChanged || riskTitleChanged);
        } else {
            setHasUnsavedChanges(false);
        }
    }, [editedTitle, editedRiskText, editedRiskTitle, actionPlans, editingIndex]);

    const validateFile = (file: File, isLeft: boolean): boolean => {
        // Check file type
        const acceptedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!acceptedTypes.includes(file.type)) {
            const errorMsg = 'Please upload a JPEG, PNG, GIF, or WebP image';
            isLeft ? setLeftImageError(errorMsg) : setRightImageError(errorMsg);
            return false;
        }

        // Check file size (e.g., limit to 5MB)
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (file.size > maxSize) {
            const errorMsg = 'File exceeds 5MB limit';
            isLeft ? setLeftImageError(errorMsg) : setRightImageError(errorMsg);
            return false;
        }

        // Clear any previous errors
        isLeft ? setLeftImageError(null) : setRightImageError(null);
        return true;
    };

    const uploadFile = async (event: any, index: number, isLeft: boolean) => {
        const file = event.target.files?.[0];
        if (!file) return;

        if (!validateFile(file, isLeft)) return;
        // Set loading state based on which side we're uploading
        if (isLeft) {
            setIsLeftImageLoading(true);
        } else {
            setIsRightImageLoading(true);
        }

        const uploadFormData = new FormData();
        uploadFormData.append("files", new File([file], `${Date.now()}-${file.name}`, { type: file.type }));

        try {
            // Using your existing mutateCustom function (you might need to import it)
            mutateCustom({
                url: `${API_URL}/api/upload`,
                method: "post",
                values: uploadFormData,
                successNotification: {
                    message: 'File uploaded successfully!',
                    type: 'success'
                },
                errorNotification: (err) => ({
                    message: `File upload failed. Please try again: ${err?.message}`,
                    type: 'error'
                })
            }, {
                onSuccess(data) {
                    if (data && data.data[0]) {
                        const uploadedImageUrl = data.data[0].url;

                        // Update the appropriate action plan based on which side (left or right)
                        const actionPlan = actionPlans[index];
                        const updatedActionPlan = { ...actionPlan };

                        if (isLeft) {
                            // Update the left image (optionConfig.optionImage)
                            updatedActionPlan.optionConfig = {
                                ...updatedActionPlan.optionConfig,
                                optionImage: uploadedImageUrl
                            };
                            setIsLeftImageLoading(false);
                        } else {
                            // Update the right image (action.image)
                            updatedActionPlan.image = uploadedImageUrl;
                            setIsRightImageLoading(false);
                        }

                        // Call the parent handler to update the action plan
                        handleEditActionPlan(updatedActionPlan);
                    }
                },
                onError() {
                    if (isLeft) {
                        setIsLeftImageLoading(false);
                    } else {
                        setIsRightImageLoading(false);
                    }
                }
            });
        } catch (error) {
            const errorMsg = 'Upload failed. Please try again.';
            if (isLeft) {
                setLeftImageError(errorMsg);
                setIsLeftImageLoading(false);
            } else {
                setRightImageError(errorMsg);
                setIsRightImageLoading(false);
            }
        }
    };

    // New function to handle edits (for saving)
    const handleSaveEdit = (index: number, outcome: string) => {
        // Get the current action plan
        const actionPlan = actionPlans[index];
        const optionConfig = actionPlan.optionConfig || {};

        // Check if any changes were made
        const titleChanged = editedTitle !== actionPlan.title;
        const riskTextChanged = editedRiskText !== optionConfig.overallRisk;
        const riskTitleChanged = editedRiskTitle !== optionConfig.riskTitle;

        // Only update if there are actual changes
        if (titleChanged || riskTextChanged || riskTitleChanged) {

            // Create the updated action plan
            const updatedActionPlan = {
                ...actionPlan,
                title: editedTitle,
                optionConfig: {
                    ...actionPlan.optionConfig,
                    riskTitle: editedRiskTitle,
                    overallRisk: editedRiskText
                }
            };

            // Pass to parent component to handle update
            handleEditActionPlan(updatedActionPlan);
        }
        setHasUnsavedChanges(false);
        setLeftImageError(null);
        setRightImageError(null);
        // Exit edit mode
        setEditingIndex(null);
        setEditedTitle("");
        setEditedRiskText("");
        setEditedRiskTitle("");
    };
    return (
        <Stack gap="20px">
            <Stack
                // direction="row"
                direction={{ xs: "column", sm: "row" }}
                alignItems={{ xs: "flex-start", md: "center" }}
                justifyContent="space-between"
                // gap="15px"
                gap={{ xs: "0px", sm: "15px" }}
            >
                <MLTypography
                    variant="h1"
                    fontSize={{ md: "32px", xs: "24px" }}
                    fontWeight={600}
                >
                    Top 3 ergo risks and recommendations
                </MLTypography>
                <MLButton
                    color="primary"
                    sx={{
                        textTransform: 'none',
                        '&:hover': {
                            backgroundColor: 'transparent',
                        }
                    }}
                    onClick={handleViewAll}
                    disableRipple
                >
                    View detailed report
                    <CustomRightArrow
                        sx={{
                            fontSize: "20px",
                            ml: 1
                        }}
                    />
                </MLButton>
            </Stack>
            <Stack gap="30px" >
                {actionPlans?.sort((a, b) => a.order - b.order).slice(0, 3).map((action: IActionPlan, index: number) => {
                    // const matchingAssessment = findMatchingAssessment(action);
                    const optionConfig = action.optionConfig || {};
                    // console.log('optionConfig: ', optionConfig);
                    const imageContainerWidth = {
                        xs: "auto",
                        // sm: "250px",
                        md: "242px",
                        lg: "280px",
                    };

                    const imageHeight = {
                        md: "182px",
                        xs: "auto"
                    };

                    return (
                        <Stack
                            key={`action-plan-${index}`}
                            sx={{
                                display: "flex",
                                gap: "30px",
                                borderRadius: "10px",
                                pt: "20px",
                                pb: "30px",
                                px: "30px",
                                border: "0.5px solid #E0E0E0",
                            }}
                        >
                            <Stack
                                direction="row"
                                justifyContent="space-between"
                                alignItems="flex-start"
                                gap="30px"
                            >
                                <Stack
                                    sx={{
                                        display: "flex",
                                        gap: "22px",
                                        flexDirection: "row",
                                        alignItems: "baseline",
                                    }}
                                    width="100%"
                                >
                                    <MLTypography
                                        sx={{
                                            fontFamily: "Syne",
                                            fontSize: { md: "40px", xs: "32px" },
                                            fontWeight: 700,
                                            color: "#7856FF",
                                            lineHeight: 1
                                        }}
                                    >
                                        0{index + 1}
                                    </MLTypography>
                                    {editingIndex === index ? (
                                        <Box width="100%">
                                            <MLInputbox
                                                label=""
                                                value={editedRiskTitle}
                                                fullWidth
                                                onChange={(e) => setEditedRiskTitle(e.target.value)}
                                                sx={{
                                                    '& .MuiInputBase-input': {
                                                        fontFamily: "Syne",
                                                        fontSize: "24px",
                                                        fontWeight: 600,
                                                        lineHeight: 1
                                                    }
                                                }}
                                            />
                                        </Box>
                                    ) : (
                                        <MLTypography
                                            sx={{
                                                fontFamily: "Syne",
                                                fontSize: "24px",
                                                fontWeight: 600,
                                                lineHeight: 1
                                            }}
                                        >
                                            {optionConfig.riskTitle}
                                        </MLTypography>
                                    )}
                                </Stack>
                                {/* Edit/Save/Cancel buttons */}
                                <Stack
                                    direction="row"
                                >
                                    {editingIndex === index ? (
                                        <>
                                            <IconButton
                                                size="small"
                                                sx={{ color: "green" }}
                                                onClick={() => handleSaveEdit(index, action.outcome)}
                                            >
                                                <Save />
                                            </IconButton>
                                            <IconButton
                                                size="small"
                                                sx={{ color: "red" }}
                                                onClick={() => {
                                                    setEditingIndex(null);
                                                    setEditedTitle("");
                                                    setEditedRiskText("");
                                                    setEditedRiskTitle("");
                                                    setLeftImageError(null);
                                                    setRightImageError(null);
                                                }}
                                            >
                                                <Close />
                                            </IconButton>
                                        </>
                                    ) : (
                                        <MLButton
                                            startIcon={<Edit />}
                                            onClick={() => {
                                                setEditingIndex(index);
                                                setEditedRiskTitle(optionConfig.riskTitle || "");
                                                setEditedRiskText(optionConfig.overallRisk || "");
                                                setEditedTitle(action.title || "");
                                            }}
                                        >
                                            Edit
                                        </MLButton>
                                    )}
                                </Stack>
                            </Stack>
                            <Stack
                                sx={{
                                    display: "flex",
                                    gap: "22px",
                                    flexDirection: { xs: "column", sm: "row" },
                                    alignItems: "baseline",
                                }}
                            >
                                <MLTypography
                                    // Hides the text but preserves the space
                                    sx={{
                                        fontFamily: "Syne",
                                        fontSize: { md: "40px", xs: "32px" },
                                        fontWeight: 700,
                                        color: "transparent",
                                        visibility: "hidden",
                                        lineHeight: 1
                                    }}
                                >
                                    0{index + 1}
                                </MLTypography>
                                <Stack
                                     sx={{
                                        display: "flex",
                                        gap: "30px",
                                        flexDirection: { lg: "row", md: "column", sm: "column", xs: "column" },
                                        alignItems: { sm: "center", xs: "center", md: "center" },
                                    }}
                                >
                                    <Stack>
                                        <Stack
                                            direction={{ sm: "row", xs: "column" }}
                                            gap="30px"
                                        >
                                            {/* left side */}
                                            <Stack
                                                gap={{ lg: "20px", xs: "10px" }}
                                                sx={{
                                                    width: imageContainerWidth,
                                                    maxWidth: "300px",
                                                    // alignSelf: "stretch",
                                                    display: "flex",
                                                    flexDirection: "column",
                                                }}
                                            >
                                                <Box
                                                    sx={{
                                                        position: "relative",
                                                        width: "100%",
                                                        flexGrow: 0,
                                                    }}>
                                                    <Stack
                                                        sx={{
                                                            position: "absolute",
                                                            top: -10,
                                                            right: -10,
                                                            width: { md: "35px", xs: "30px" },
                                                            height: "auto",
                                                            zIndex: 10,
                                                        }}
                                                    >
                                                        <img src={cross} alt="cross" />
                                                    </Stack>
                                                    {!isLeftImageLoading ?
                                                        <>
                                                            <Box
                                                                component="img"
                                                                sx={{
                                                                    borderStyle: "solid",
                                                                    border: "0.5px solid #E0E0E0",
                                                                    backgroundColor: "#fcfcfc",
                                                                    borderRadius: "8px",
                                                                    overflow: "hidden",
                                                                    width: "100%",
                                                                    height: imageHeight,
                                                                    objectFit: "contain",
                                                                }}
                                                                src={optionConfig?.optionImage}
                                                                alt="actionPlanFix"
                                                            />
                                                            {leftImageError && editingIndex === index && (
                                                                <MLTypography
                                                                    variant="caption"
                                                                    color="error"
                                                                >
                                                                    {leftImageError}
                                                                </MLTypography>
                                                            )}
                                                        </>
                                                        :
                                                        <Skeleton
                                                            animation="wave"
                                                            variant="rounded"
                                                            sx={{
                                                                width: { lg: "300px", md: "242px", xs: "100%" },
                                                                height: { md: "182px", xs: "120px" },
                                                                aspectRatio: { xs: "4/3" },
                                                            }}
                                                        />
                                                    }
                                                    {editingIndex === index && (
                                                        <>
                                                            <input
                                                                type="file"
                                                                ref={leftFileInputRefs[index]}
                                                                style={{ display: 'none' }}
                                                                accept="image/*"
                                                                onChange={(e) => uploadFile(e, index, true)}
                                                            />
                                                            <IconButton
                                                                size="small"
                                                                sx={{
                                                                    position: "absolute",
                                                                    top: "10px",
                                                                    right: "30px",
                                                                    backgroundColor: "rgba(255, 255, 255, 0.8)",
                                                                    borderRadius: "50%",
                                                                }}
                                                                onClick={() => leftFileInputRefs[index].current?.click()}
                                                                disabled={isLeftImageLoading}
                                                            >
                                                                {!isLeftImageLoading && <ErCamera />}
                                                            </IconButton>
                                                        </>
                                                    )}
                                                </Box>
                                                <Box
                                                    sx={{
                                                        width: "100%",
                                                        minHeight: { xs: "auto", sm: "48px" },
                                                    }}
                                                >
                                                    {editingIndex === index ? (
                                                        <MLInputbox
                                                            label=''
                                                            fullWidth
                                                            multiline
                                                            value={editedRiskText}
                                                            onChange={(e) => setEditedRiskText(e.target.value)}
                                                        />
                                                    ) :
                                                        <MLTypography
                                                            variant={"body1"}
                                                            fontSize={{ md: "16px", xs: "14px" }}
                                                            fontWeight={400}
                                                            lineHeight={1.2}
                                                            textAlign="left"
                                                        >
                                                            {optionConfig?.overallRisk}
                                                        </MLTypography>
                                                    }
                                                </Box>
                                            </Stack>

                                            {/* right side */}
                                            <Stack
                                                gap={{ lg: "20px", xs: "10px" }}
                                                sx={{
                                                    width: imageContainerWidth,
                                                    maxWidth: "300px",
                                                    display: "flex",
                                                    flexDirection: "column",
                                                }}
                                            >
                                                <Box
                                                    sx={{
                                                        position: "relative",
                                                        width: "100%",
                                                    }}>

                                                    <Stack
                                                        sx={{
                                                            position: "absolute",
                                                            top: -10,
                                                            right: -10,
                                                            width: { md: "35px", xs: "30px" },
                                                            height: "auto",
                                                            zIndex: 10,
                                                        }}
                                                    >
                                                        <img src={tick} alt="tick" />
                                                    </Stack>
                                                    {!isRightImageLoading ?
                                                        <>
                                                            < Box
                                                                component="img"
                                                                sx={{
                                                                    borderStyle: "solid",
                                                                    border: "0.5px solid #E0E0E0",
                                                                    backgroundColor: "#fcfcfc",
                                                                    borderRadius: "8px",
                                                                    overflow: "hidden",
                                                                    width: "100%",
                                                                    height: imageHeight,
                                                                    objectFit: "contain",
                                                                }}
                                                                src={action.image}
                                                                alt="actionPlanFix"
                                                            />
                                                            {rightImageError && editingIndex === index && (
                                                                <MLTypography
                                                                    variant="caption"
                                                                    color="error"
                                                                >
                                                                    {rightImageError}
                                                                </MLTypography>
                                                            )}
                                                        </>
                                                        :
                                                        <Skeleton
                                                            animation="wave"
                                                            variant="rounded"
                                                            sx={{
                                                                width: { lg: "300px", md: "242px", xs: "100%" },
                                                                height: { md: "182px", xs: "120px" },
                                                                aspectRatio: { xs: "4/3" },
                                                            }}
                                                        />
                                                    }
                                                    {editingIndex === index && (
                                                        <>
                                                            <input
                                                                type="file"
                                                                ref={rightFileInputRefs[index]}
                                                                style={{ display: 'none' }}
                                                                accept="image/*"
                                                                onChange={(e) => uploadFile(e, index, false)}
                                                            />
                                                            <IconButton
                                                                size="small"
                                                                sx={{
                                                                    position: "absolute",
                                                                    top: "10px",
                                                                    right: "30px",
                                                                    backgroundColor: "rgba(255, 255, 255, 0.8)",
                                                                    borderRadius: "50%",
                                                                }}
                                                                onClick={() => rightFileInputRefs[index].current?.click()}
                                                                disabled={isRightImageLoading}
                                                            >
                                                                {!isRightImageLoading && <ErCamera />}
                                                            </IconButton>
                                                        </>
                                                    )}
                                                </Box>

                                                <Box
                                                    sx={{
                                                        width: "100%",
                                                        minHeight: { xs: "auto", sm: "48px" },
                                                        position: "relative",
                                                    }}
                                                >
                                                    {editingIndex === index ? (
                                                        <MLInputbox
                                                            label=''
                                                            fullWidth
                                                            multiline
                                                            value={editedTitle}
                                                            onChange={(e) => setEditedTitle(e.target.value)}
                                                        />
                                                    ) : (
                                                        <MLTypography
                                                            variant={"body1"}
                                                            fontSize={{ md: "16px", xs: "14px" }}
                                                            fontWeight={400}
                                                            lineHeight={1.2}
                                                            textAlign="left"
                                                        >
                                                            {action.title}
                                                        </MLTypography>
                                                    )}
                                                </Box>
                                            </Stack>
                                        </Stack>
                                        {hasUnsavedChanges && editingIndex === index && (
                                            <MLTypography
                                                variant="caption"
                                                fontSize="12px"
                                                color="warning.main"
                                                sx={{ mt: 1 }}
                                            >
                                                Unsaved changes
                                            </MLTypography>
                                        )}
                                    </Stack>
                                    <Divider
                                        orientation="horizontal"
                                        flexItem
                                    />
                                    {/* Action buttons section */}
                                    <Stack
                                        alignItems="center"
                                        flex={1}
                                    >
                                        <Stack
                                            gap="20px"
                                            alignItems="center"
                                        >
                                            <MLTypography
                                                variant="body1"
                                                fontSize="14px"
                                                fontWeight={400}
                                                lineHeight={1.2}
                                            >
                                                Add {" "}
                                                <span
                                                    style={{
                                                        fontWeight: 600,
                                                        fontSize: "16px",
                                                        lineHeight: 1.2,
                                                        color: "#31C100",
                                                    }}
                                                >
                                                    {action.points} points
                                                </span>
                                                {" "} to your ergo score
                                            </MLTypography>

                                            <Stack width={{ sm: "auto", md: "100%" }}>
                                                {!action.isCompleted ? (
                                                    <MLButton
                                                        variant="contained"
                                                        color="secondary"
                                                        onClick={() => handleActionPlanComplete(action.outcome, action.points, false)}
                                                    >
                                                        Mark As Done
                                                    </MLButton>
                                                ) : (
                                                    <MLButton
                                                        variant="outlined"
                                                        color="secondary"
                                                        sx={{
                                                            backgroundColor: "#98E080",
                                                            border: "1px solid black",
                                                            color: "black",
                                                            '&:hover': {
                                                                backgroundColor: "#98E080",
                                                                border: "1px solid black",
                                                                color: "black"
                                                            }
                                                        }}
                                                    >
                                                        Completed
                                                    </MLButton>
                                                )}
                                                <MLButton
                                                    disabled={action.isCompleted}
                                                    sx={{ paddingX: "20px" }}
                                                    onClick={() => setIsNeedHelpModalOpened(true)}
                                                >
                                                    Need help?
                                                </MLButton>
                                            </Stack>
                                            {action.isCompleted && (
                                                <MLButton
                                                    variant="text"
                                                    color="primary"
                                                    onClick={() => handleActionPlanComplete(action.outcome, action.points, true)}
                                                    sx={{
                                                        textTransform: "none",
                                                        fontSize: "14px",
                                                        padding: { xs: "8px 16px", sm: 0 },
                                                        fontWeight: 600,
                                                        '&:hover': {
                                                            backgroundColor: "transparent",
                                                            color: "#5E3DCF"
                                                        }
                                                    }}
                                                >
                                                    Reset this task
                                                </MLButton>
                                            )}
                                        </Stack>
                                    </Stack>
                                </Stack>
                            </Stack>
                        </Stack>
                    );
                })}
            </Stack>
            <AskAQuestion
                openAskAQuestionModal={isNeedHelpModalOpened}
                handleClose={() => setIsNeedHelpModalOpened(false)}
                isActionPlan={true}
            />
        </Stack >
    )
}

export default ENErgoRisksAndRecommendations;
