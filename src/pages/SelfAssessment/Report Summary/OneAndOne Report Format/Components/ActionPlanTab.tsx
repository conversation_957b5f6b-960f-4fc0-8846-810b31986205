import React, { useState } from "react";
import { Box, Divider, Stack } from "@mui/material";
import MLTypography from "../../../../../components/ui/MLTypography/MLTypography";
import MLButton from "../../../../../components/ui/MLButton/MLButton";
import { ConsultScheduleSection } from "../../../Report/ReportStyleA";
import ScoreProgressBar from "./ScoreProgressBar";
import AskAQuestion from "../../../../Dashboard/AskAQuestion";

interface ActionPlanTabProps {
    ergoScore: number;
    achievedActionScore: number;
    actionPlans: Array<{
        title: string;
        outcome: string;
        text: string | string[];
        image: string;
        points: number;
        order?: number;
        isCompleted?: boolean;
    }>;
    isActionCompleted: (outcome: string) => boolean;
    onActionComplete: (outcome: string, points: number, isCurrentlyCompleted: boolean) => void;
}

const ActionPlanTab: React.FC<ActionPlanTabProps> = ({
    ergoScore,
    achievedActionScore,
    actionPlans,
    isActionCompleted,
    onActionComplete
}) => {
    const [isNeedHelpModalOpened, setIsNeedHelpModalOpened] = useState<boolean>(false)

    const handleNeedHelp = () => {
        setIsNeedHelpModalOpened(true);
    }

    return (
        <Stack mt={"55px"} gap={"60px"}>
            <Stack direction={{ lg: "row", xs: "column" }} gap={"20px"}>
                <Stack width={{ lg: "50%", xs: "100%" }}
                    gap={"10px"}>
                    <MLTypography
                        variant="h1"
                        fontSize={{ xs: "28px", sm: "40px" }}
                        fontWeight={600}
                        lineHeight={1}
                    >
                        Your quick Action Plan
                    </MLTypography>
                    <MLTypography
                        variant="body1"
                        fontSize={{ xs: "16px", sm: "20px" }}
                        lineHeight={"130%"}
                    >
                        Complete the following actions to improve your ergo score and health!
                    </MLTypography>
                </Stack>
                <Stack width={{ lg: "50%", xs: "100%" }}>
                    <ScoreProgressBar score={(ergoScore ?? 0) + (achievedActionScore ?? 0)} />
                </Stack>
            </Stack>

            {/* Action Plan Items */}
            <Stack gap="30px">
                {actionPlans?.sort((a, b) => (a.order || 0) - (b.order || 0)).map((action, index) => (
                    <>
                        <Stack
                            key={`action-plan-${index}`}
                        >
                            {/* Header with number and title */}
                            <Stack
                                sx={{
                                    display: "flex",
                                    gap: "22px",
                                    flexDirection: "row",
                                    alignItems: { md: "baseline", xs: "center" },
                                    mb: 3
                                }}
                            >
                                <MLTypography
                                    sx={{
                                        fontFamily: "Syne",
                                        fontSize: { md: "40px", xs: "32px" },
                                        fontWeight: 700,
                                        color: "#7856FF",
                                        lineHeight: 1
                                    }}
                                >
                                    0{index + 1}
                                </MLTypography>
                                <MLTypography
                                    sx={{
                                        fontFamily: "Syne",
                                        fontSize: { sm: "24px", xs: "18px" },
                                        fontWeight: 600,
                                        lineHeight: 1
                                    }}
                                >
                                    {action.title}
                                </MLTypography>
                                {action.isCompleted && (
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 34 34" fill="none">
                                        <g clipPath="url(#clip0_17600_19854)">
                                            <path d="M6.81417 18.4734L1.0625 25.5001L6.375 26.5626L8.5 32.9376L14.0817 24.0692" stroke="#333333" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                            <path d="M27.1855 18.4734L32.9371 25.5001L27.6246 26.5626L25.4996 32.9376L19.918 24.0692" stroke="#333333" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                            <path d="M17 24.4375C23.46 24.4375 28.6875 19.21 28.6875 12.75C28.6875 6.29 23.46 1.0625 17 1.0625C10.54 1.0625 5.3125 6.29 5.3125 12.75C5.3125 19.21 10.54 24.4375 17 24.4375Z" fill="#DFFF32" stroke="#333333" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                            <path d="M22.8369 8.72668L15.0877 16.4759C14.8894 16.6742 14.6202 16.7875 14.3369 16.7875C14.0536 16.7875 13.7844 16.6742 13.5861 16.4759L11.1494 14.0392" stroke="#333333" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_17600_19854">
                                                <rect width="34" height="34" fill="white" />
                                            </clipPath>
                                        </defs>
                                    </svg>
                                )}
                            </Stack>

                            {/* Content section */}
                            <Stack
                                direction={{ xs: "column", sm: "row" }}
                                spacing={3}
                                alignItems={{ xs: "center", md: "flex-start" }}
                            >
                                {/* Image */}
                                <Box
                                    sx={{
                                        position: "relative",
                                        width: { xs: "100%", sm: "240px", md: "280px" },
                                        maxWidth: "300px"
                                    }}
                                >
                                    <Box
                                        component="img"
                                        sx={{
                                            borderStyle: "solid",
                                            border: "0.5px solid #E0E0E0",
                                            backgroundColor: "#fcfcfc",
                                            borderRadius: "8px",
                                            overflow: "hidden",
                                            width: "100%",
                                            height: { xs: "auto", md: "182px" },
                                            objectFit: "contain",
                                        }}
                                        src={action.image}
                                        alt={action.title}
                                    />
                                </Box>

                                {/* Text and buttons */}
                                <Stack
                                    direction={{ sm: "column", md: "row" }}
                                    justifyContent="space-between"
                                    gap={"20px"}
                                    flex={1}
                                >
                                    {/* Action description */}
                                    <Stack width={{ md: "70%", sm: "100%" }}>
                                        {
                                            typeof action.text === "string" ? (
                                                <ul style={{ paddingLeft: "18px", marginBlock: 0 }}>
                                                    <MLTypography
                                                        component="div"
                                                        variant='body1'
                                                        fontSize={{ md: "16px", xs: "14px" }}
                                                        fontWeight={400}
                                                        lineHeight={1.2}
                                                        sx={{
                                                            "& > div > ul > li": {
                                                                marginBottom: "8px"
                                                            },
                                                            "& > div > ul > li:last-child": {
                                                                marginBottom: 0,
                                                            }
                                                        }}
                                                    >
                                                        <div dangerouslySetInnerHTML={{ __html: action.text }} />
                                                    </MLTypography>
                                                </ul>
                                            ) : (
                                                <ul style={{ paddingLeft: "18px", marginBlock: 0 }}>
                                                    {action.text.map((textPoint, key) => (
                                                        <li key={textPoint + key}>
                                                            <MLTypography
                                                                component="div"
                                                                variant='body1'
                                                                fontSize={{ md: "16px", xs: "14px" }}
                                                                fontWeight={400}
                                                                lineHeight={1.2}
                                                                sx={{
                                                                    "& > div > ul > li": {
                                                                        marginBottom: "8px"
                                                                    },
                                                                    "& > div > ul > li:last-child": {
                                                                        marginBottom: 0,
                                                                    }
                                                                }}
                                                            >
                                                                {textPoint}
                                                            </MLTypography>
                                                        </li>
                                                    ))}
                                                </ul>
                                            )
                                        }
                                    </Stack>

                                    {/* Action buttons section */}
                                    <Stack
                                        width={{ md: "25%", sm: "100%" }}
                                        spacing={2}

                                        alignItems={{ xs: "center", sm: "center" }}
                                        justifyContent="space-between"
                                    >
                                        <MLTypography
                                            variant="body1"
                                            fontSize="14px"
                                            fontWeight={400}
                                            lineHeight={1.2}
                                        >
                                            Add{" "}
                                            <span
                                                style={{
                                                    fontWeight: 600,
                                                    fontSize: "16px",
                                                    lineHeight: 1.2,
                                                    color: "#31C100",
                                                }}
                                            >
                                                {action.points} points
                                            </span>
                                            {" "}to your ergo score
                                        </MLTypography>

                                        <Stack width={{ sm: "auto", md: "100%" }}>
                                            {!isActionCompleted(action.outcome) ? (
                                                <MLButton
                                                    variant="contained"
                                                    color="secondary"
                                                    onClick={() => onActionComplete(action.outcome, action.points, false)}
                                                >
                                                    Mark As Done
                                                </MLButton>
                                            ) : (
                                                <MLButton
                                                    variant="outlined"
                                                    color="secondary"
                                                    sx={{
                                                        backgroundColor: "#98E080",
                                                        border: "1px solid black",
                                                        color: "black",
                                                        '&:hover': {
                                                            backgroundColor: "#98E080",
                                                            border: "1px solid black",
                                                            color: "black"
                                                        }
                                                    }}
                                                >
                                                    Completed
                                                </MLButton>
                                            )}
                                        </Stack>

                                        {isActionCompleted(action.outcome) && (
                                            <MLButton
                                                variant="text"
                                                color="primary"
                                                onClick={() => onActionComplete(action.outcome, action.points, true)}
                                                sx={{
                                                    textTransform: "none",
                                                    fontSize: "14px",
                                                    padding: { xs: "8px 16px", sm: 0 },
                                                    fontWeight: 600,
                                                    '&:hover': {
                                                        backgroundColor: "transparent",
                                                        color: "#5E3DCF"
                                                    }
                                                }}
                                            >
                                                Reset this task
                                            </MLButton>
                                        )}

                                        <MLButton
                                            variant="text"
                                            color="primary"
                                            sx={{
                                                textTransform: "none",
                                                fontSize: "14px",
                                                padding: { xs: "8px 16px", sm: 0 },
                                                fontWeight: 600,
                                                '&:hover': {
                                                    backgroundColor: "transparent",
                                                    color: "#5E3DCF"
                                                }
                                            }}
                                        >
                                            Need help?
                                        </MLButton>
                                    </Stack>
                                </Stack>
                            </Stack>
                        </Stack>
                        {index < actionPlans.length - 1 &&
                            <Divider sx={{ borderColor: "#E0E0E0" }} />
                        }
                    </>
                ))}
            </Stack>

            <ConsultScheduleSection
                isLeftBorder={true}
                hasContainer={false}
                handleNeedHelp={handleNeedHelp}
            />
            <AskAQuestion
                openAskAQuestionModal={isNeedHelpModalOpened}
                handleClose={() => setIsNeedHelpModalOpened(false)}
                isActionPlan={false}
            />
        </Stack>
    );
};

export default ActionPlanTab;
