import React from "react";
import { Box } from "@mui/material";
import MLTypography from "../../../../../components/ui/MLTypography/MLTypography";

// Define the ScoreProgressBar component
interface ScoreProgressBarProps {
  score?: number;
}

const ScoreProgressBar: React.FC<ScoreProgressBarProps> = ({ score = 0 }) => {
  const adjustedScore = score >= 99 ? 100 : score;

  return (
    <Box sx={{ position: "relative" }}>
      <MLTypography
        variant="subtitle1"
        fontWeight={600}
        lineHeight={1.2}
        fontSize={{ xs: "14px", sm: "20px" }}
      >
        Ergo Score: {adjustedScore}/100
      </MLTypography>
      <Box
        sx={{
          height: "25px",
          borderRadius: "20px",
          backgroundColor: "#EAEAEA",
          overflow: "hidden",
          marginTop: "10px"
        }}
      >
        <Box
          sx={{
            width: `${adjustedScore}%`,
            height: "100%",
            borderRadius: "20px",
            backgroundColor: "#31C100",
            transition: "width 0.6s ease-in-out",
          }}
        />
      </Box>

      <Box
        sx={{
          position: "absolute",
          bottom: "-12px",
          left: { md: `${adjustedScore - 3}%`, xs: `${adjustedScore - 6}%` },
          transform: "translateX(-50%)",
          color: "black",
          padding: "8px 14px",
          borderRadius: "22px",
          transition: "left 0.6s ease-in-out",
          zIndex: 1,
        }}
      >
        <MLTypography variant="subtitle1" fontWeight={500}>
          {adjustedScore}
        </MLTypography>
      </Box>
    </Box>
  );
};

export default ScoreProgressBar;