import React, { useState, useContext, useEffect } from "react";
import { <PERSON>, <PERSON>ack, Grid, Divider, ButtonBase } from "@mui/material";
import { TabContext, TabPanel } from "@mui/lab";
import MLTypography from "../../../../components/ui/MLTypography/MLTypography";
import MLContainer from "../../../../components/ui/MLMaxWidthContainer/MLMaxWidthContainer";
import MLTab from "../../../../components/ui/MLTab/MLTab";
import MLTabs from "../../../../components/ui/MLTabs/MLTabs";
import { IGeneratedReport, IReportData, RiskLevelType } from "../../Report/Report";
import IDiscomfort from "../../models/IDiscomfort";
import { AuthContext } from "../../../../contexts/authContext/AuthContext";
import { ViewModeContext } from "../../../../contexts/ViewModeContext/ViewModeContext";
import { useList } from "@refinedev/core";
import ActionPlanTab from "./Components/ActionPlanTab";
import ErgoRiskAnalysisTab from "./Components/ErgoRiskAnalysisTab";
import TopSection from "./Components/TopSection";


// Define tab types
enum ReportTab {
  ACTION_PLAN = "action_plan",
  ERGO_RISK_ANALYSIS = "ergo_risk_analysis"
}

interface OneAndOneReportFormatProps {
  report: IReportData;
  reportPdfUrl?: string;
  isPdfGenerating?: boolean;
  caseId?: number;
  discomfort?: IDiscomfort;
  ergonomicScoreText?: string;
  handleActionPlanComplete: (outcome: string, points: number) => void;
  showProductPrice?: boolean;
  enableProductLink?: boolean;
  isAiAssessment?: boolean;
  aiCaseImages?: {
    chairImage?: { url: string };
    chairDeskImage?: { url: string };
  };
  generatedReport: IGeneratedReport[];
}

const OneAndOneReportFormat: React.FC<OneAndOneReportFormatProps> = ({
  report,
  reportPdfUrl = "",
  isPdfGenerating = false,
  caseId,
  discomfort = {},
  handleActionPlanComplete,
  showProductPrice = false,
  enableProductLink = false,
  isAiAssessment = false,
  aiCaseImages = {},
  generatedReport
}) => {
  const { userDetails: loggedUserDetails } = useContext(AuthContext);
  const { isEmployeeView } = useContext(ViewModeContext);
  const isLoggedUserIsEmployee = loggedUserDetails?.role?.name.toLowerCase() === "employee";

  const [currentTab, setCurrentTab] = useState<ReportTab>(ReportTab.ACTION_PLAN);
  const [completedActions, setCompletedActions] = useState<Record<string, boolean>>({});
  const [scoreLevel, setScoreLevel] = useState<RiskLevelType>('low');

  // Process the data
  const ergoScore = report?.ergoPostureScore || 0;
  const actionPlans = report?.actionPlans || [];
  const achievedActionScore = report?.achievedActionScore || 0;

  // Handle action plan completion
  const onActionComplete = (outcome: string, points: number, isCurrentlyCompleted: boolean) => {
    // Toggle completion state and add/remove points as needed
    if (isCurrentlyCompleted) {
      // If already completed, mark as incomplete and subtract points
      setCompletedActions(prev => {
        const newState = { ...prev };
        delete newState[outcome]; // Remove from completed actions
        return newState;
      });
      // Call the handler with negative points to subtract
      handleActionPlanComplete(outcome, -points);
    } else {
      // If not completed, mark as completed and add points
      setCompletedActions(prev => ({ ...prev, [outcome]: true }));
      // Call the handler with positive points to add
      handleActionPlanComplete(outcome, points);
    }
  };

  useEffect(() => {
    if (report?.ergoPostureScore != undefined) {
      if (report?.ergoPostureScore > 80) {
        setScoreLevel('high');
      } else if (report?.ergoPostureScore >= 50) {
        setScoreLevel('medium');
      } else {
        setScoreLevel('low');
      }
    }
  }, [report]);

  const { data: ergonomicScoreTextData, isLoading: ergonomicScoreTextDataLoading } = useList<{ text: string }>({
    resource: "common-text-contents",
    filters: [
      {
        field: "textContentName",
        operator: "eq",
        value: `ergonomicScore-${scoreLevel}`,
      }
    ],
    pagination: {
      pageSize: 1,
    },
    queryOptions: {
      enabled: !!scoreLevel,
      onError: (error: any) => {
        console.error("ergonomicScoreTextData loading error:", error);
      }
    }
  });

  const isActionCompleted = (outcome: string): boolean => {
    // Check if completed in component state (for immediate UI update)
    if (completedActions[outcome]) return true;

    // Otherwise check if completed in report data
    const actionPlan = actionPlans.find(ap => ap.outcome === outcome);
    return actionPlan?.isCompleted || false;
  };

  return (
    <Stack marginBottom={6}>
      {/* Top Section with Score and Uploaded Images */}
      <MLContainer>
        <TopSection
          ergoScore={ergoScore}
          ergonomicScoreText={ergonomicScoreTextData?.data[0]?.text || "Your ergonomic score indicates your current ergonomic setup."}
          isAiAssessment={isAiAssessment}
          aiCaseImages={aiCaseImages}
          reportPdfUrl={reportPdfUrl}
          isPdfGenerating={isPdfGenerating}
          caseId={caseId}
          isLoggedUserIsEmployee={isLoggedUserIsEmployee}
          loggedUserDetails={loggedUserDetails}
          isEmployeeView={isEmployeeView}
          generatedReport={generatedReport}
        />
      </MLContainer>
      {/* Tabs Section */}
      <Box>
        <MLContainer>
          {/* Desktop Tabs */}
          <Box sx={{ display: { xs: "none", sm: "block" } }}>
            <MLTabs
              value={currentTab}
              onChange={(_, value) => setCurrentTab(value)}
            >
              <MLTab
                label="Action Plan"
                value={ReportTab.ACTION_PLAN}
                sx={{ textTransform: "none", fontSize: "16px" }}
              />
              <MLTab
                label="Ergo Risk Analysis"
                value={ReportTab.ERGO_RISK_ANALYSIS}
                sx={{ textTransform: "none", fontSize: "16px" }}
              />
            </MLTabs>
          </Box>
          {/* Mobile Tabs */}
          <Stack
            direction="row"
            spacing={2}
            sx={{
              display: { xs: "flex", sm: "none" },
              marginBottom: 2
            }}
          >
            <ButtonBase
              component={Box}
              sx={{
                flex: 1,
                padding: 1.5,
                borderRadius: "5px",
                border: currentTab === ReportTab.ACTION_PLAN ? "1px solid #7856FF" : "0.5px solid #9C9C9C",
                backgroundColor: currentTab === ReportTab.ACTION_PLAN ? "#E3DDFF" : "transparent",
                transition: "all 0.3s ease"
              }}
              onClick={() => setCurrentTab(ReportTab.ACTION_PLAN)}
            >
              <MLTypography
                variant="body1"
                fontSize="14px"
                fontWeight={600}
                textAlign="center"
              >
                Action Plan
              </MLTypography>
            </ButtonBase>

            <ButtonBase
              component={Box}
              sx={{
                flex: 1,
                padding: 1.5,
                borderRadius: "5px",
                border: currentTab === ReportTab.ERGO_RISK_ANALYSIS ? "1px solid #7856FF" : "0.5px solid #9C9C9C",
                backgroundColor: currentTab === ReportTab.ERGO_RISK_ANALYSIS ? "#E3DDFF" : "transparent",
                transition: "all 0.3s ease"
              }}
              onClick={() => setCurrentTab(ReportTab.ERGO_RISK_ANALYSIS)}
            >
              <MLTypography
                variant="body1"
                fontSize="14px"
                fontWeight={600}
                textAlign="center"
              >
                Ergo Risk Analysis
              </MLTypography>
            </ButtonBase>
          </Stack>
        </MLContainer>
        <TabContext value={currentTab}>
          {/* Action Plan Tab */}
          <TabPanel sx={{ p: 0 }} value={ReportTab.ACTION_PLAN}>
            <MLContainer>
              <ActionPlanTab
                ergoScore={ergoScore}
                achievedActionScore={achievedActionScore}
                actionPlans={actionPlans}
                isActionCompleted={isActionCompleted}
                onActionComplete={onActionComplete}
              />
            </MLContainer>
          </TabPanel>

          {/* Ergo Risk Analysis Tab */}
          <TabPanel sx={{ p: 0 }} value={ReportTab.ERGO_RISK_ANALYSIS}>
            <ErgoRiskAnalysisTab
              report={report}
              discomfort={discomfort}
            />
          </TabPanel>
        </TabContext>
      </Box>
    </Stack>
  );
};

export default OneAndOneReportFormat;