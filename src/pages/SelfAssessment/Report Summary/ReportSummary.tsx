import React, { use<PERSON><PERSON>back, useContext, useEffect, useMemo, useState } from "react";
import { IGeneratedReport, IReportData, RiskLevelType } from "../Report/Report";
import { Box, Button, ButtonBase, Divider, Grid, IconButton, Stack } from "@mui/material";
import { desktop, tablet } from "../../../responsiveStyles";
import MLTypography from "../../../components/ui/MLTypography/MLTypography";
import ScoreMeter from "../Report/components/ScoreMeter";
import Markdown from "react-markdown";
import { CrudFilter, useBack, useGetIdentity, useGo, useList, useOne, useParsed, useUpdate } from "@refinedev/core";
import ISelfAssessmentCase from "../models/ISelfAssessmentCase";
import IIdentity from "../models/IIdentity";
import Loading from "../../Loading/Loading";
import { formatEnumString } from "../../../utils/enumUtils";
import MLTabs from "../../../components/ui/MLTabs/MLTabs";
import MLTab from "../../../components/ui/MLTab/MLTab";
import QuickSummary from "./Tabs/QuickSummary";
import DetailedReport from "./Tabs/DetailedReport";
import ActionPlan from "./Tabs/ActionPlan";
import IDiscomfort from "../models/IDiscomfort";
import { generatePDF, sortBodyParts, updateAnalyticIssueResolve } from "../Report/reportUtils";
import { DownloadViewPdfReport } from "../Report/components/DownloadViewPdfReport";
import RetakeAssessment from "../Report/components/RetakeAssessment";
import { debounce } from 'lodash';
import MLBanner from "../../../components/ui/MLBanner/MLBanner";
import MLContainer from "../../../components/ui/MLMaxWidthContainer/MLMaxWidthContainer";
import { StatusMessage } from "../../MyAssessments/myAssessmentUtils";
import MLButton from "../../../components/ui/MLButton/MLButton";
import { AuthContext } from "../../../contexts/authContext/AuthContext";
import { ErChevronleft } from "@mindlens/ergo-icons";
import { ViewModeContext } from "../../../contexts/ViewModeContext/ViewModeContext";
import OneAndOneReportFormat from "./OneAndOne Report Format/OneAndOneReportFormat";


export enum TabTitle {
  QUICK_SUMMARY = "quick_summary",
  DETAILED_REPORT = "detailed_report",
  ACTION_PLAN = "action_plan",
}

interface TabPanelProps {
  children?: React.ReactNode;
  currentTab: TabTitle;
  value: TabTitle;
}
function CustomTabPanel(props: TabPanelProps) {
  const { children, value, currentTab, ...other } = props;

  return (
    <div role="tabpanel" hidden={value !== currentTab} {...other}>
      {value === currentTab && <>{children}</>}
    </div>
  );
}


const ReportSummary = () => {
  const { data: identity, isLoading: isIdentityLoading } = useGetIdentity<IIdentity>();
  const { userDetails: loggedUserDetails } = useContext(AuthContext);
  const { mutate: mutateUpdateReport } = useUpdate();
  const go = useGo();

  const [caseId, setCaseId] = useState<number>();
  const [reportId, setReportId] = useState<number>();
  const [report, setReport] = useState<IReportData>();
  const [reportPdfUrl, setReportPdfUrl] = useState<string>("");
  const [ergonomicScoreText, setErgonomicScoreText] = useState<string>("");
  const [scoreLevel, setScoreLevel] = useState<RiskLevelType>('low');
  const [currentTab, setCurrentTab] = useState<TabTitle>(TabTitle.QUICK_SUMMARY);
  const [discomfort, setDiscomfort] = useState<IDiscomfort>({});
  const [isPdfGenerating, setIsPdfGenerating] = useState<boolean>(false);
  const [hasCalledPdfGeneration, setHasCalledPdfGeneration] = useState<boolean>(false);
  const { id } = useParsed();
  const back = useBack();
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [pdfError, setPdfError] = useState<string | null>(null);
  const [redirecting, setRedirecting] = useState<boolean>(false);
  const [initialLoadComplete, setInitialLoadComplete] = useState<boolean>(false);
  const isLoggedUserIsEmployee = loggedUserDetails?.role?.name.toLowerCase() === "employee"
  const { isEmployeeView } = useContext(ViewModeContext);
  const [reportFormatType, setReportFormatType] = useState<"bodyPartBased" | "oneOnOneMapping">("bodyPartBased");

  // Define the filters with proper typing
  const { data: selfAssessmentCaseData, isLoading: isSelfAssessmentCaseLoading, refetch: refetchCase } =
    useList<ISelfAssessmentCase>({
      resource: "self-assessment-cases",
      meta: {
        populate: {
          employee: {
            fields: ["id", "name"],
            populate: {
              organization: {
                fields: ["name", "showProductPrice", "enableProductLink"]
              }
            }
          }
        }
      },
      filters: [
        ...(id
          ? [{
            field: "id",
            operator: "eq" as const,  // Type assertion to specific operator
            value: id
          }]
          : [
            {
              field: "employee.userID",
              operator: "eq" as const,
              value: identity?.id
            },
            {
              field: "isCompleted",
              operator: "eq" as const,
              value: true
            }
          ]
        ),
      ] as CrudFilter[],  // Type assertion for the entire array
      sorters: [
        {
          field: "updatedAt",
          order: "desc"
        }
      ],
      pagination: {
        pageSize: 1,
      },
      queryOptions: {
        onError: (error: any) => {
          setErrorMessage(`Failed to load assessment case: ${error.message || 'Unknown error occurred'}`);
          console.error("Self assessment case loading error:", error);
        }
      }
    });

  const { data: userData, isLoading: isUserDataLoading } = useOne({
    resource: "users",
    id: identity?.id,
    meta: {
      populate: {
        organization: {
          fields: ["id"],
          populate: {
            domainConfig: {
              fields: ["domain"],
            },
            admin_report_setting: {
              fields: ["reportSetting"],
            }
          },
        },
      },
    },
  });

  const {
    data: adminReportSettings,
    isLoading: adminReportSettingsLoading,
    refetch,
  } = useList({
    resource: "admin-report-settings",
    filters: [
      {
        field: "organization.id",
        operator: "eq",
        value: userData?.data?.organization?.id ?? "",
      },
    ],
    meta: {
      populate: "*",
    },
    queryOptions: {
      enabled: !!userData?.data?.organization?.id,
    },
  });

  useEffect(() => {
    if (adminReportSettings?.data) {
      const report = adminReportSettings.data[0]?.reportSetting.reportFormat;

      if (report?.bodyPartBased) {
        setReportFormatType("bodyPartBased");
      } else if (report?.oneOnOneMapping) {
        setReportFormatType("oneOnOneMapping");
      } else {
        setReportFormatType("bodyPartBased"); // default if neither is true or missing
      }
    }
  }, [adminReportSettings]);

  // Rest of the component remains the same
  const selfAssessmentCase = useMemo(() =>
    selfAssessmentCaseData?.data ?? [],
    [selfAssessmentCaseData]
  );

  interface IAiCase {
    chairDeskImage: { url: string }
    chairImage: { url: string }
  }

  const { data: aiCaseData } =
    useList<IAiCase>({
      resource: "ai-cases",
      meta: {
        populate: {
          chairImage: {
            fields: ["*"],
          },
          chairDeskImage: {
            fields: ["*"],
          },
        }
      },
      filters: [
        {
          field: "selfAssessmentCase",
          operator: "eq" as const,
          value: selfAssessmentCase[0] ? selfAssessmentCase[0].id : undefined
        }
      ] as CrudFilter[],
      sorters: [
        {
          field: "updatedAt",
          order: "desc"
        }
      ],
      pagination: {
        pageSize: 1,
      },
      queryOptions: {
        enabled: selfAssessmentCase[0] ? selfAssessmentCase[0].isAiAssessment : false
      }
    });

  const aiCase = useMemo(() =>
    aiCaseData?.data[0] ?? [],
    [aiCaseData]
  );


  const { data: generatedReportData, isLoading: generatedReportDataLoading, refetch: refetchReport } =
    useList<IGeneratedReport>({
      resource: "self-assessment-reports",
      filters: [
        {
          field: "selfAssessmentCase",
          operator: "eq",
          value: caseId ?? 0,
        },
      ],
      pagination: {
        pageSize: 1,
      },
      queryOptions: {
        enabled: !!caseId,
        onError: (error: any) => {
          setErrorMessage(`Failed to load self-assessment report: ${error.message || 'Unknown error occurred'}`);
          console.error("self-assessment-reports loading error:", error);
        }
      }
    });

  const { data: ergonomicScoreTextData, isLoading: ergonomicScoreTextDataLoading } = useList<{ text: string }>({
    resource: "common-text-contents",
    filters: [
      {
        field: "textContentName",
        operator: "eq",
        value: `ergonomicScore-${scoreLevel}`,
      }
    ],
    pagination: {
      pageSize: 1,
    },
    queryOptions: {
      enabled: !!scoreLevel,
      onError: (error: any) => {
        setErrorMessage(`Failed to load score description: ${error.message || 'Unknown error occurred'}`);
        console.error("ergonomicScoreTextData loading error:", error);
      }
    }
  });

  const generatedReport = useMemo(() =>
    generatedReportData?.data ?? [],
    [generatedReportData]
  );


  useEffect(() => {
    if (selfAssessmentCase.length == 1) {
      setCaseId(selfAssessmentCase[0].id);
      setDiscomfort(selfAssessmentCase[0].discomfort);
    }
  }, [selfAssessmentCase]);

  useEffect(() => {
    // Only proceed if we have a report and it's not loading
    if (generatedReport.length !== 1 || generatedReportDataLoading) {
      return;
    }

    try {
      const currentReport = generatedReport[0];

      // Process the report data
      const processedReport = {
        ...currentReport.generatedReport,
        potentialRiskPart: sortBodyParts(currentReport.generatedReport.potentialRiskPart)
      };

      setReport(processedReport);
      setReportId(currentReport.id);

      // Handle PDF URL setting
      if (currentReport.generatedPdfUrl) {
        // If we already have a URL, use it
        console.log("Using existing PDF URL:", currentReport.generatedPdfUrl);
        setReportPdfUrl(currentReport.generatedPdfUrl);
      } else if (!hasCalledPdfGeneration && !isUserDataLoading) {
        // If no URL exists and we haven't started generation, generate it
        const generateReportPdf = async () => {
          setIsPdfGenerating(true);
          setHasCalledPdfGeneration(true);

          try {
            const pdfUrl = await generatePDF(
              processedReport.actionPlans,
              processedReport.achievedActionScore,
              processedReport.ergonomicScoreText,
              processedReport.ergoPostureScore,
              processedReport.mergedProducts,
              processedReport.reportGeneratedBodyParts,
              processedReport.username,
              processedReport.goodHabits,
              processedReport.potentialRiskPart,
              currentReport.id,
              currentReport.generatedReport.uploadedImages,
              userData?.data.organization.admin_report_setting ? userData?.data.organization.admin_report_setting.reportSetting.reportFormat.oneOnOneMapping : false,
              userData?.data.organization.domainConfig ? userData?.data.organization.domainConfig.domain : undefined,
            );

            console.log("Generated new PDF URL:", pdfUrl);
            setReportPdfUrl(pdfUrl);
            setIsPdfGenerating(false);
          } catch (error) {
            setPdfError("Failed to generate PDF report. You can try again later.");
            setIsPdfGenerating(false);
            console.error("PDF generation error:", error);
          }
        };

        generateReportPdf();
      }
    } catch (error) {
      setErrorMessage("Error processing report data. Please try again.");
      console.error("Report processing error:", error);
    }
  }, [generatedReport, generatedReportDataLoading, hasCalledPdfGeneration, userData]);

  useEffect(() => {
    if (report?.ergoPostureScore != undefined) {
      if (report?.ergoPostureScore > 80) {
        setScoreLevel('high');
      } else if (report?.ergoPostureScore >= 50) {
        setScoreLevel('medium');
      } else {
        setScoreLevel('low');
      }
    }
  }, [report]);

  useEffect(() => {
    if (ergonomicScoreTextData && ergonomicScoreTextData.data.length > 0) {
      setErgonomicScoreText(ergonomicScoreTextData.data[0].text);
    } else if (!ergonomicScoreTextDataLoading && ergonomicScoreTextData?.data.length === 0) {
      setErgonomicScoreText("Your ergonomic score indicates your current ergonomic setup. Improving your setup will help reduce discomfort and increase productivity.");
    }
  }, [ergonomicScoreTextData, ergonomicScoreTextDataLoading]);

  const debouncedNavigate = useCallback(
    debounce(() => {
      setRedirecting(true);
      go({ to: "/self-assessment/getting-started" });
    }, 300),
    []
  );

  useEffect(() => {
    // Redirect if:
    // 1. Not loading anymore AND
    // 2. Either no data exists OR data array is empty
    if (!isSelfAssessmentCaseLoading &&
      (!selfAssessmentCaseData?.data || selfAssessmentCaseData.data.length === 0)) {
      debouncedNavigate();
    }

    return () => {
      debouncedNavigate.cancel();
    };
  }, [debouncedNavigate, selfAssessmentCaseData, isSelfAssessmentCaseLoading]);

  // Set initialLoadComplete after all critical data is loaded or attempted to load
  useEffect(() => {
    if (!isIdentityLoading && !isSelfAssessmentCaseLoading) {
      if (caseId) {
        // If we have a case ID, wait for the report loading to complete
        if (!generatedReportDataLoading) {
          setInitialLoadComplete(true);
        }
      } else {
        // No case ID means we're done loading the critical path
        setInitialLoadComplete(true);
      }
    }
  }, [isIdentityLoading, isSelfAssessmentCaseLoading, generatedReportDataLoading, caseId]);

  const handleActionPlanComplete = async (outcome: string, points: number) => {
    const newReport = report; // Create a proper copy of the report
    const index = newReport?.actionPlans.findIndex(
      (ap) => ap.outcome === outcome,
    );

    if (index !== undefined && index >= 0 && newReport) {
      // Toggle completion state based on points (positive = complete, negative = incomplete)
      const isCompleting = points > 0;
      newReport.actionPlans[index].isCompleted = isCompleting;

      // Add or subtract points from achievedActionScore
      newReport.achievedActionScore += points;

      // Update the report in the database
      mutateUpdateReport({
        resource: "self-assessment-reports",
        id: reportId!,
        successNotification: false,
        values: {
          generatedReport: newReport,
        },
      });

      // Update local state
      setReport({ ...newReport });

      // Only call analytics when completing an action, not when uncompleting
      if (isCompleting) {
        updateAnalyticIssueResolve(
          selfAssessmentCase[0].employee.organization.name,
          selfAssessmentCase[0].employee.id
        );
      }
    }
  };

  const handleRetry = () => {
    setErrorMessage(null);
    setPdfError(null);
    refetchCase();
    if (caseId) {
      refetchReport();
    }
  };

  // Combine all loading states for the master loading state
  const isLoading = adminReportSettingsLoading || isIdentityLoading || isSelfAssessmentCaseLoading ||
    redirecting || (caseId && generatedReportDataLoading) || !initialLoadComplete;

  // While in the loading state, only show the loading spinner
  if (isLoading) {
    return (
      <>
        <MLBanner backgroundColor='#E3DDFF' title="Self Assessment Report" />
        <Box height={640} display="flex" justifyContent="center" alignItems="center">
          <Loading />
        </Box>
      </>
    );
  }

  // Handle authentication error
  if (!identity && !isIdentityLoading) {
    return (
      <>
        {
          isLoggedUserIsEmployee &&
          <MLBanner backgroundColor='#E3DDFF' title="Self Assessment Report" />
        }
        <Box height={640} display="flex" justifyContent="center" alignItems="center">
          <MLContainer>
            <StatusMessage
              title="Authentication Required"
              message="You need to be logged in to view your assessment report. Please log in and try again."
              type="warning"
            />
          </MLContainer>
        </Box>
      </>
    );
  }

  // Handle case where assessment case is not found (should be handled by redirect but just in case)
  if (!isSelfAssessmentCaseLoading && selfAssessmentCase.length === 0) {
    return (
      <>
        {
          isLoggedUserIsEmployee &&
          <MLBanner backgroundColor='#E3DDFF' title="Self Assessment Report" />
        }
        <Box height={640} display="flex" justifyContent="center" alignItems="center">
          <MLContainer>
            <StatusMessage
              title="No Assessment Found"
              message="Please complete a self-assessment to view your report."
              type="info"
            />
            <Box display="flex" justifyContent="center" mt={3}>
              <MLButton variant="contained" onClick={() => go({ to: "/self-assessment/getting-started" })}>
                Start Assessment
              </MLButton>
            </Box>
          </MLContainer>
        </Box>
      </>
    );
  }

  // Handle case where assessment exists but report doesn't
  if (!generatedReportDataLoading && generatedReport.length === 0 && selfAssessmentCase.length > 0 &&
    (isLoggedUserIsEmployee || loggedUserDetails?.employeeView || isEmployeeView)) {
    return (
      <>
        <MLBanner backgroundColor='#E3DDFF' title="Self Assessment Report" />
        <Stack height={640} display="flex" justifyContent="center" alignItems="center">
          <StatusMessage
            title="Report Unavailable"
            message="Your assessment report is not available. This may happen if your assessment was not fully completed."
            type="warning"
          />
          <Box display="flex" justifyContent="flex-start" alignItems={'center'} mx={1} mt={3}>
            <MLButton variant="contained" onClick={() => go({ to: "/self-assessment/getting-started" })}>
              Retake Assessment
            </MLButton>
          </Box>
        </Stack>
      </>
    );
  }

  if (!report) {
    return (
      <>
        <MLBanner backgroundColor='#E3DDFF' title="Self Assessment Report" />
        <Box height={640} display="flex" justifyContent="center" alignItems="center">
          <Loading />
        </Box>
      </>
    );
  }
  const bodyPartBased = false; // old one is true
  return report && (
    <Stack>
      <MLBanner backgroundColor='#E3DDFF' title="Self Assessment Report" />
      {reportFormatType === "bodyPartBased" ?
        <Stack
          direction="column"
          height="100%"
        >
          {/* error message section */}
          {(errorMessage || pdfError) && (
            <Stack
              sx={{
                marginBottom: "20px"
              }}
            >
              <MLContainer>
                {errorMessage && (
                  <StatusMessage
                    title="Error Loading Report"
                    message={errorMessage}
                    type="error"
                  />
                )}
                {pdfError && !errorMessage && (
                  <StatusMessage
                    title="PDF Generation Issue"
                    message={pdfError}
                    type="warning"
                  />
                )}
                <Box display="flex" justifyContent="center" mt={2}>
                  <MLButton variant="outlined" onClick={handleRetry}>
                    Retry
                  </MLButton>
                </Box>
              </MLContainer>
            </Stack>
          )}

          {/* ergo score section */}
          <Stack
            sx={{
              paddingX: {
                // lg: desktop.contentContainer.paddingX,
                // md: tablet.contentContainer.paddingX,
                // xs: tablet.contentContainer.paddingX,
              },
            }}
          >
            <MLContainer>
              <Stack direction={{ xs: "column", lg: "row" }} gap={"30px"}>
                {/** Start: Report ergo score */}
                <Stack
                  direction={"row"}
                  sx={{
                    alignItems: "center",
                    backgroundColor: "#6C4AF4",
                    borderRadius: "10px"
                  }}
                >
                  <Grid container marginY={"30px"}>
                    <Grid
                      item
                      md={selfAssessmentCase[0].isAiAssessment ? 4 : 3}
                      lg={selfAssessmentCase[0].isAiAssessment ? 4 : 3}
                      sx={{
                        display: { xs: "none", md: "flex", lg: "flex", xl: "flex" },
                        position: "relative"
                      }}
                    >
                      <Stack
                        sx={{
                          position: "absolute",
                          left: { xs: "-180px", lg: "-160px" },
                          width: { xs: "430px", md: "430px", lg: "450px", xl: "450px" },
                          height: { xs: "430px", md: "430px", lg: "450px", xl: "450px" },
                        }}
                      >
                        <ScoreMeter percentage={report?.ergoPostureScore ?? 0}></ScoreMeter>
                      </Stack>
                    </Grid>
                    <Grid
                      item
                      md={selfAssessmentCase[0].isAiAssessment ? 8 : 9}
                      lg={selfAssessmentCase[0].isAiAssessment ? 8 : 9}
                      xl={selfAssessmentCase[0].isAiAssessment ? 8 : 9}
                      sx={{
                        paddingX: { xs: "16px", sm: "32px" },
                      }}
                    >
                      {report?.ergoPostureScore != undefined ? (
                        <>
                          <Box
                            // paddingY={"30px"}
                            sx={{
                              // borderStyle: "solid",
                              // borderWidth: "0.5px",
                              // borderColor: "#C1C1C1",
                              // borderRadius: "8px",
                              minHeight: "385px",
                            }}
                          >
                            {
                              !isLoggedUserIsEmployee &&
                              <Stack mb={1} direction={{ xs: "column", sm: "row" }} alignItems={{ xs: "flex-start", sm: "center" }}>
                                <MLTypography
                                  variant="body1"
                                  fontSize={"20px"}
                                  color={"#DFFF30"}
                                >
                                  Assessment report for:
                                </MLTypography>
                                <MLTypography
                                  variant="body1"
                                  fontSize={"20px"}
                                  fontWeight={600}
                                  lineHeight={1}
                                  color={"#DFFF30"}
                                >
                                  {generatedReport[0]?.generatedReport?.username.toUpperCase()}
                                </MLTypography>
                              </Stack>
                            }
                            <Stack
                              marginBottom={"20px"}>

                              {/** desktop view ergo score meter */}
                              <Stack
                                marginBottom={"30px"}
                                direction={"row"}
                                display={{ xs: "none", sm: "flex" }}
                                sx={{
                                  alignItems: "center",
                                }}
                              >
                                <Stack>
                                  <MLTypography
                                    variant="body1"
                                    fontSize={"22px"}
                                    fontWeight={400}
                                    lineHeight={1}
                                    color={"white"}
                                  >
                                    Your Ergo score:
                                  </MLTypography>
                                  <Stack
                                    direction={"row"}
                                    sx={{
                                      alignItems: "center",
                                    }}
                                  >
                                    <MLTypography
                                      variant="h1"
                                      fontSize={"72px"}
                                      fontWeight={600}
                                      lineHeight={1}
                                      color={"white"}
                                    >
                                      {report.ergoPostureScore}
                                    </MLTypography>
                                    <MLTypography
                                      variant="h1"
                                      fontSize={"35px"}
                                      fontWeight={600}
                                      lineHeight={1}
                                      color={"white"}
                                    >
                                      /100
                                    </MLTypography>
                                  </Stack>
                                </Stack>
                                <Divider
                                  orientation="vertical"
                                  flexItem
                                  sx={{
                                    display: { xs: "none", sm: "flex" },
                                    backgroundColor: "white",
                                    marginX: "30px"
                                  }}
                                />
                                <Stack gap={"8px"} marginTop={{ xs: "16px", sm: 0 }}>
                                  <MLTypography
                                    variant="body1"
                                    fontSize={"22px"}
                                    fontWeight={400}
                                    lineHeight={1}
                                    color={"white"}
                                  >
                                    Higher score =
                                  </MLTypography>
                                  <MLTypography
                                    variant="body1"
                                    fontSize={"22px"}
                                    fontWeight={400}
                                    lineHeight={1}
                                    color={"#DFFF30"}
                                  >
                                    better ergonomics
                                  </MLTypography>
                                </Stack>
                              </Stack>

                              {/** mobile view ergo score meter */}
                              <Stack
                                direction={"column"}
                                display={{ xs: "flex", sm: "none" }}
                              >
                                <Stack>
                                  <MLTypography
                                    variant="body1"
                                    fontSize={"22px"}
                                    fontWeight={400}
                                    lineHeight={1}
                                    color={"white"}
                                  >
                                    Your Ergo score
                                  </MLTypography>

                                  <Grid container
                                    marginY={"20px"}
                                    minHeight={{ xs: "180px" }}
                                  >
                                    <Grid
                                      item
                                      xs={5}
                                      sx={{
                                        position: "relative"
                                      }}
                                    >
                                      <Stack
                                        sx={{
                                          position: "absolute",
                                          left: { xs: "-70px", },
                                          width: { xs: "180px", },
                                          height: { xs: "180px", },
                                        }}
                                      >
                                        <ScoreMeter
                                          percentage={report?.ergoPostureScore ?? 0}
                                          lineWidth={6}
                                        />
                                      </Stack>
                                    </Grid>
                                    <Grid
                                      item
                                      xs={7}
                                    >
                                      <Stack
                                        direction={"column"}
                                        height={"100%"}
                                        sx={{
                                          justifyContent: "center",
                                          alignItems: "flex-start",
                                        }}
                                      >
                                        <Stack direction={"row"}
                                          sx={{
                                            alignItems: "center",
                                          }}
                                        >
                                          <MLTypography
                                            variant="h1"
                                            fontSize={"64px"}
                                            fontWeight={600}
                                            lineHeight={1}
                                            color={"white"}
                                          >
                                            {report.ergoPostureScore}
                                          </MLTypography>
                                          <MLTypography
                                            variant="h1"
                                            fontSize={"32px"}
                                            fontWeight={600}
                                            lineHeight={1}
                                            color={"white"}
                                          >
                                            /100
                                          </MLTypography>
                                        </Stack>
                                        <Divider
                                          orientation="horizontal"
                                          flexItem
                                          sx={{
                                            backgroundColor: "white",
                                            marginY: "15px"
                                          }}
                                        />
                                        <Stack gap={"8px"}>
                                          <MLTypography
                                            variant="body1"
                                            fontSize={"16px"}
                                            fontWeight={500}
                                            lineHeight={1}
                                            color={"white"}
                                          >
                                            Higher score =
                                          </MLTypography>
                                          <MLTypography
                                            variant="body1"
                                            fontSize={"16px"}
                                            fontWeight={500}
                                            lineHeight={1}
                                            color={"#DFFF30"}
                                          >
                                            better ergonomics
                                          </MLTypography>
                                        </Stack>
                                      </Stack>
                                    </Grid>
                                  </Grid>
                                </Stack>
                              </Stack>

                              <Stack>
                                <MLTypography
                                  variant="body1"
                                  fontSize={"18px"}
                                  fontWeight={600}
                                  lineHeight={"27px"}
                                  color={"white"}
                                >
                                  What it Means?
                                </MLTypography>
                                <MLTypography
                                  variant="body1"
                                  fontSize={{ xs: "16px", sm: "18px" }}
                                  fontWeight={300}
                                  lineHeight={"27px"}
                                  color={"white"}
                                >
                                  Ergo Score is calculated after comparing the current furniture, equipment and posture against ergonomics standards and best practices.
                                </MLTypography>
                              </Stack>
                            </Stack>
                            <Stack position={"relative"}>
                              {/* <SideHighlight
                              isLeftBorder={true}
                              color={report.ergoPostureScore > 80 ? "white" : report.ergoPostureScore < 50 ? "#d03f43" : "#eab34b"}
                              width={"8px"}
                              onlyLeftSide={true}
                            /> */}
                              <Stack
                              //  paddingX={"30px"}
                              >
                                <Markdown
                                  components={{
                                    p: ({ children }) => (
                                      <MLTypography
                                        variant="body1"
                                        fontSize={{ xs: "16px", sm: "18px" }}
                                        fontWeight={300}
                                        color={"white"}
                                      >
                                        {children}
                                      </MLTypography>
                                    ),
                                  }}
                                >
                                  {ergonomicScoreText || "Your ergonomic score indicates your current ergonomic setup."}
                                </Markdown>
                              </Stack>
                            </Stack>
                          </Box>

                          <Stack
                            direction={{ xs: "column", sm: "row" }}
                            marginTop={"30px"}
                            gap={"12px"}
                            sx={{
                              alignItems: { xs: "stretch", sm: "center" },
                              justifyContent: "space-between",
                            }}
                          >
                            <DownloadViewPdfReport reportPdfUrl={reportPdfUrl} isPdfGenerating={isPdfGenerating} />
                            {
                              (isLoggedUserIsEmployee || loggedUserDetails?.employeeView || isEmployeeView) &&
                              <RetakeAssessment caseId={caseId} />
                            }
                          </Stack>
                        </>
                      ) : (
                        <Box p={4} textAlign="center">
                          <MLTypography color="white">
                            Score calculation is not available for this report.
                          </MLTypography>
                        </Box>
                      )}
                    </Grid>
                  </Grid>
                </Stack>
                {/** End: Report ergo score */}
              </Stack>
              {generatedReport[0].generatedReport.uploadedImages && (
                <Stack gap={"20px"} marginTop={"45px"}>
                  <MLTypography variant="h1" fontSize={"24px"} fontWeight={700} lineHeight={1.2}>
                    Uploaded Images
                  </MLTypography>
                  <Stack
                    direction={"row"}
                    gap={"10px"}
                  >
                    {(generatedReport[0].generatedReport.uploadedImages.map((img) => (
                      // <Box
                      //   component="img"
                      //   sx={{
                      //     border: "0.5px solid #9C9C9C",
                      //     borderRadius: "10px",
                      //     maxWidth: { xs: (generatedReport[0].generatedReport.uploadedImages?.length || 0) > 2 ? "30%" : "45%", sm: "100%" },
                      //     width: { xs: "150px", md: "200px" },
                      //     height: "auto",
                      //   }}
                      //   src={img}
                      //   alt="AI analysis chair photo"
                      // />
                      <Box
                        component="img"
                        sx={{
                          border: "0.5px solid #9C9C9C",
                          borderRadius: "10px",
                          maxWidth: { xs: (generatedReport[0].generatedReport.uploadedImages?.length || 0) > 2 ? "30%" : "45%", sm: "100%" },
                          width: { xs: "150px", md: "150px" },
                          maxHeight: { xs: "150px", sm: "180px" },
                          height: "auto",
                          objectFit: "cover",
                        }}
                        src={img}
                        alt="AI analysis chair photo"
                      />
                    )))}
                  </Stack>
                </Stack>
              )}
            </MLContainer>
          </Stack>
          {/* ergo score section end*/}

          {/* tab section */}
          <MLContainer>
            <Stack display={{ xs: "none", sm: "flex" }}>
              <MLTabs
                value={currentTab}
                onChange={(e, val) => setCurrentTab(val)}
                defaultValue={formatEnumString(TabTitle.QUICK_SUMMARY)}
                variant="scrollable"
                sx={{
                  maxWidth: { xs: "85vw", sm: "100vw" },
                  marginY: "45px",
                }}
              >
                {Object.values(TabTitle).map((tab) => (
                  <MLTab
                    key={tab}
                    value={tab}
                    label={formatEnumString(tab)}
                  />
                ))}
              </MLTabs>
            </Stack>

            {/** Tabs for mobile view */}
            <Stack direction={"row"} display={{ xs: "flex", sm: "none" }}
              sx={{
                marginY: "30px",
                gap: "15px",
                width: "100%" // Ensure the parent takes full width
              }}
            >
              {/* Quick Summary Tab */}
              <ButtonBase
                component={Stack}
                sx={{
                  border: currentTab === TabTitle.QUICK_SUMMARY ? "1px solid #7856FF" : "0.5px solid #9C9C9C",
                  backgroundColor: currentTab === TabTitle.QUICK_SUMMARY ? "#E3DDFF" : "",
                  borderRadius: "5px",
                  padding: "15px",
                  justifyContent: "center",
                  alignItems: "center",
                  flex: 1, // This makes each child take equal width
                  minWidth: 0, // This prevents overflow
                  transition: "background-color 0.3s",
                  "&:hover": {
                    backgroundColor: currentTab === TabTitle.QUICK_SUMMARY ? "#D9D0FF" : "#F5F5F5",
                  },
                }}
                onClick={() => setCurrentTab(TabTitle.QUICK_SUMMARY)}
                disableRipple={false}
                centerRipple
              >
                <MLTypography
                  variant="body1"
                  fontSize={"14px"}
                  fontWeight={600}
                  textAlign={"center"}
                >
                  Quick Summary
                </MLTypography>
              </ButtonBase>

              {/* Detailed Report Tab */}
              <ButtonBase
                component={Stack}
                sx={{
                  border: currentTab === TabTitle.DETAILED_REPORT ? "1px solid #7856FF" : "0.5px solid #9C9C9C",
                  backgroundColor: currentTab === TabTitle.DETAILED_REPORT ? "#E3DDFF" : "",
                  borderRadius: "5px",
                  padding: "15px",
                  justifyContent: "center",
                  alignItems: "center",
                  flex: 1, // This makes each child take equal width
                  minWidth: 0, // This prevents overflow
                  transition: "background-color 0.3s",
                  "&:hover": {
                    backgroundColor: currentTab === TabTitle.DETAILED_REPORT ? "#D9D0FF" : "#F5F5F5",
                  },
                }}
                onClick={() => setCurrentTab(TabTitle.DETAILED_REPORT)}
                disableRipple={false}
                centerRipple
              >
                <MLTypography
                  variant="body1"
                  fontSize={"14px"}
                  fontWeight={600}
                  textAlign={"center"}
                >
                  Detailed Report
                </MLTypography>
              </ButtonBase>

              {/* Action Plan Tab */}
              <ButtonBase
                component={Stack}
                sx={{
                  border: currentTab === TabTitle.ACTION_PLAN ? "1px solid #7856FF" : "0.5px solid #9C9C9C",
                  backgroundColor: currentTab === TabTitle.ACTION_PLAN ? "#E3DDFF" : "",
                  borderRadius: "5px",
                  padding: "15px",
                  justifyContent: "center",
                  alignItems: "center",
                  flex: 1, // This makes each child take equal width
                  minWidth: 0, // This prevents overflow
                  transition: "background-color 0.3s",
                  "&:hover": {
                    backgroundColor: currentTab === TabTitle.ACTION_PLAN ? "#D9D0FF" : "#F5F5F5",
                  },
                }}
                onClick={() => setCurrentTab(TabTitle.ACTION_PLAN)}
                disableRipple={false}
                centerRipple
              >
                <MLTypography
                  variant="body1"
                  fontSize={"14px"}
                  fontWeight={600}
                  textAlign={"center"}
                >
                  Action Plan
                </MLTypography>
              </ButtonBase>
            </Stack>
          </MLContainer>
          {/* tab section */}

          <CustomTabPanel value={TabTitle.QUICK_SUMMARY} currentTab={currentTab as TabTitle}>
            <MLContainer sx={{ mb: 6 }}>
              <QuickSummary
                report={report!}
                handleActionPlanComplete={handleActionPlanComplete}
                setCurrentTab={setCurrentTab}
                showProductPrice={selfAssessmentCase[0]?.employee?.organization?.showProductPrice}
                enableProductLink={selfAssessmentCase[0]?.employee?.organization?.enableProductLink}
              />
            </MLContainer>
          </CustomTabPanel>
          <CustomTabPanel value={TabTitle.DETAILED_REPORT} currentTab={currentTab as TabTitle}>
            <DetailedReport
              discomfort={discomfort}
              report={report!}
              showProductPrice={selfAssessmentCase[0]?.employee?.organization?.showProductPrice}
              enableProductLink={selfAssessmentCase[0]?.employee?.organization?.enableProductLink}
            />
          </CustomTabPanel>
          <CustomTabPanel value={TabTitle.ACTION_PLAN} currentTab={currentTab as TabTitle}>
            <MLContainer sx={{ mb: 6 }}>
              <ActionPlan
                report={report!}
                handleActionPlanComplete={handleActionPlanComplete}
              />
            </MLContainer>
          </CustomTabPanel>
        </Stack >
        :
        <OneAndOneReportFormat
          report={report}
          reportPdfUrl={reportPdfUrl}
          isPdfGenerating={isPdfGenerating}
          caseId={caseId}
          discomfort={discomfort}
          ergonomicScoreText={ergonomicScoreText}
          handleActionPlanComplete={handleActionPlanComplete}
          showProductPrice={selfAssessmentCase[0]?.employee?.organization?.showProductPrice}
          enableProductLink={selfAssessmentCase[0]?.employee?.organization?.enableProductLink}
          isAiAssessment={selfAssessmentCase[0]?.isAiAssessment}
          aiCaseImages={'chairImage' in aiCase ? aiCase : {}}
          generatedReport={generatedReport}
        />}
    </Stack>
  )
};

export default ReportSummary;