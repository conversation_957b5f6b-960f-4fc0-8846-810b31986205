import React, { useEffect, useState } from "react";
import { IReportData } from "../../Report/Report";
import { LayoutA, LayoutB } from "../../Report/ReportStyleA";
import { Stack, useMediaQuery } from "@mui/material";
import MLToggleButtonGroup from "../../../../components/ui/MLToggleButtonGroup/MLToggleButtonGroup";
import MLToggleButton from "../../../../components/ui/MLToggleButton/MLToggleButton";
import BodyFront from "../../Discomfort/BodyFront";
import BodyBack from "../../Discomfort/BodyBack";
import MLTypography from "../../../../components/ui/MLTypography/MLTypography";
import IDiscomfort from "../../models/IDiscomfort";
import { desktop, tablet } from "../../../../responsiveStyles";
import GoodHabit from "./GoodHabits";
import MLContainer from "../../../../components/ui/MLMaxWidthContainer/MLMaxWidthContainer";

interface DetailedReportProp {
  report: IReportData;
  discomfort: IDiscomfort;
  showProductPrice: boolean;
  enableProductLink: boolean;
}
const DetailedReport = ({
  report,
  discomfort,
  showProductPrice,
  enableProductLink,
}: DetailedReportProp) => {
  const useVerySmallScreen = () => {
    return useMediaQuery("(max-width:350px)");
  };
  let bodyPartCounter = 1;
  const lowRiskParts = Object.entries(report?.potentialRiskPart ?? {}).filter(
    ([, value]) => value.riskLevel === "low",
  );
  const mediumRiskParts = Object.entries(
    report?.potentialRiskPart ?? {},
  ).filter(([, value]) => value.riskLevel === "medium");
  const highRiskParts = Object.entries(
    report?.potentialRiskPart ?? {},
  ).filter(([, value]) => value.riskLevel === "high");

  const [isCollapseOpened, setIsCollapseOpened] = useState<boolean>(false);
  const [isDetailed, setIsDetailed] = useState<boolean>(false);
  const [isCollapsedSections, setIsCollapsedSections] = useState<{
    [sectionKey: string]: boolean;
  }>({});
  const isVerySmallScreen = useVerySmallScreen();

  useEffect(() => {
    if (Object.values(isCollapsedSections).some((value) => value === true)) {
      setIsCollapseOpened(true);
    } else {
      setIsCollapseOpened(false);
    }
  }, [isCollapsedSections]);

  const handleCollapseToggle = (section: string) => {
    setIsCollapsedSections((prevState) => ({
      ...prevState,
      [section]: !prevState[section], // Toggle the specific section
    }));
  };

  return (
    <Stack>
      {/* <Stack direction={"row"} spacing={"30px"} marginTop={"35px"}> */}
      <Stack
        sx={{
          paddingX: {
            lg: desktop.contentContainer.paddingX,
            md: tablet.contentContainer.paddingX,
            xs: tablet.contentContainer.paddingX,
          },
          paddingY: "30px",
          backgroundColor: "#f1f1f1",
          display: {
            xs: "none",
            sm: "none",
            md: "none",
            lg: "flex",
            xl: "flex",
          },
        }}
      >
        <MLContainer>
          <Stack
            direction="row"
            gap={"45px"}
          >
            <Stack direction={"column"} gap={"30px"}>
              <Stack>
                <MLTypography
                  variant="body1"
                  fontSize={"25px"}
                  lineHeight={1.2}
                  fontWeight={500}
                  marginBottom={"20px"}
                >
                  Potential risk of injury by body area
                </MLTypography>
                {highRiskParts.length > 0 && (
                  <Stack gap={"10px"}>
                    <Stack
                      direction="row"
                      sx={{
                        width: "fit-content",
                        backgroundColor: "#C40000",
                        borderRadius: "34px",
                        paddingX: "35px",
                        paddingY: "8px",
                      }}
                    >
                      <MLTypography
                        variant="body1"
                        fontSize={"14px"}
                        fontWeight={600}
                        lineHeight={1}
                        color={"white"}
                      >
                        High Risk
                      </MLTypography>
                    </Stack>
                    {highRiskParts.map(([bodyPart], index) => (
                      <Stack
                        key={`high-${index}`}
                        direction={"row"}
                        spacing={"20px"}
                      >
                        <MLTypography
                          variant="h1"
                          fontSize={"36px"}
                          fontWeight={500}
                          lineHeight={1}
                        >
                          {(bodyPartCounter++).toString().padStart(2, "0")}.
                        </MLTypography>
                        <MLTypography
                          variant="h1"
                          fontSize={"36px"}
                          fontWeight={500}
                          lineHeight={1}
                        >
                          <a
                            href={`#${bodyPart}`}
                            style={{
                              color: "#704bf4",
                              textDecoration: "none",
                            }}
                          >
                            {bodyPart}
                          </a>
                        </MLTypography>
                      </Stack>
                    ))}
                  </Stack>
                )}
              </Stack>
              <Stack>
                {mediumRiskParts.length > 0 && (
                  <Stack gap={"10px"}>
                    <Stack
                      direction="row"
                      sx={{
                        width: "fit-content",
                        backgroundColor: "#FF7A00",
                        borderRadius: "34px",
                        paddingX: "35px",
                        paddingY: "8px",
                      }}
                    >
                      <MLTypography
                        variant="body1"
                        fontSize={"14px"}
                        fontWeight={600}
                        lineHeight={1}
                        color={"black"}
                      >
                        Medium Risk
                      </MLTypography>
                    </Stack>
                    {mediumRiskParts.map(([bodyPart], index) => (
                      <Stack
                        key={`medium-${index}`}
                        direction={"row"}
                        spacing={"20px"}
                      >
                        <MLTypography
                          variant="h1"
                          fontSize={"36px"}
                          fontWeight={500}
                          lineHeight={1}
                        >
                          {(bodyPartCounter++).toString().padStart(2, "0")}.
                        </MLTypography>
                        <MLTypography
                          variant="h1"
                          fontSize={"36px"}
                          fontWeight={500}
                          lineHeight={1}
                        >
                          <a
                            href={`#${bodyPart}`}
                            style={{
                              color: "#704bf4",
                              textDecoration: "none",
                            }}
                          >
                            {bodyPart}
                          </a>
                        </MLTypography>
                      </Stack>
                    ))}
                  </Stack>
                )}
              </Stack>
              <Stack>
                {lowRiskParts.length > 0 && (
                  <Stack gap={"6px"}>
                    <Stack
                      direction="row"
                      sx={{
                        width: "fit-content",
                        backgroundColor: "#FFC700",
                        borderRadius: "34px",
                        paddingX: "35px",
                        paddingY: "8px",
                      }}
                    >
                      <MLTypography
                        variant="body1"
                        fontSize={"14px"}
                        fontWeight={600}
                        lineHeight={1}
                        color={"black"}
                      >
                        Low Risk
                      </MLTypography>
                    </Stack>
                    {lowRiskParts.map(([bodyPart], index) => (
                      <Stack
                        key={`high-${index}`}
                        direction={"row"}
                        spacing={"20px"}
                      >
                        <MLTypography
                          variant="h1"
                          fontSize={"36px"}
                          fontWeight={500}
                          lineHeight={1}
                        >
                          {(bodyPartCounter++).toString().padStart(2, "0")}.
                        </MLTypography>
                        <MLTypography
                          variant="h1"
                          fontSize={"36px"}
                          fontWeight={500}
                          lineHeight={1}
                        >
                          <a
                            href={`#${bodyPart}`}
                            style={{
                              color: "#704bf4",
                              textDecoration: "none",
                            }}
                          >
                            {bodyPart}
                          </a>
                        </MLTypography>
                      </Stack>
                    ))}
                  </Stack>
                )}
              </Stack>
            </Stack>
            <Stack direction={"row"} spacing={"20px"}>
              <Stack>
                <BodyFront
                  selectedParts={{ ...discomfort.reportGenerated }}
                />
              </Stack>
              <Stack>
                <BodyBack
                  selectedParts={{ ...discomfort.reportGenerated }}
                />
              </Stack>
            </Stack>
          </Stack>
        </MLContainer>
      </Stack>

      {/* main content */}
      <Stack>
        {/* detailed/snapshot stick button */}
        <Stack
          sx={{
            display: isCollapseOpened ? "block" : "none",
            position: "sticky",
            top: "70px",
            zIndex: 100,
            backgroundColor: "transparent",
          }}
        >
          <MLToggleButtonGroup
            color="primary"
            exclusive
            size="small"
            value={isDetailed}
            onChange={(event, newValue: boolean | null) => {
              if (newValue !== null) setIsDetailed(newValue);
            }}
            sx={{
              alignItems: "center",
              display: { xs: "flex", md: "none" },
              justifyContent: "flex-end",
              paddingY: 1,
              paddingRight: "20px"
            }}
          >
            <MLToggleButton sx={{ backgroundColor: "white" }} value={false}>
              Snapshot
            </MLToggleButton>
            <MLToggleButton sx={{ backgroundColor: "white" }} value={true}>
              Detailed
            </MLToggleButton>
          </MLToggleButtonGroup>
        </Stack>
        {/* detailed/snapshot stick button */}

        {Object.entries(report?.potentialRiskPart ?? {}).map(([bodyPart, value], index) => {
          const isLeftBorder = index % 2 === 0; // left right alternating
          return value.causedBys.length >= 3 ? (
            <div style={{ backgroundColor: "white" }} key={index + bodyPart}>
              <LayoutA
                bodyPart={bodyPart}
                value={value}
                index={index}
                isLeftBorder={isLeftBorder}
                isDetailed={isDetailed}
                isCollapsedSections={isCollapsedSections}
                isVerySmallScreen={isVerySmallScreen}
                handleCollapseToggle={handleCollapseToggle}
                showProductPrice={showProductPrice}
                enableProductLink={enableProductLink}
              />
            </div>
          ) : (
            <div style={{ backgroundColor: "white" }} key={index + bodyPart}>
              <LayoutB
                bodyPart={bodyPart}
                value={value}
                index={index}
                isLeftBorder={isLeftBorder}
                isDetailed={isDetailed}
                isCollapsedSections={isCollapsedSections}
                isVerySmallScreen={isVerySmallScreen}
                handleCollapseToggle={handleCollapseToggle}
                showProductPrice={showProductPrice}
                enableProductLink={enableProductLink}
              />
            </div>
          );
        })}
      </Stack>
      {/* main content */}
      <MLContainer sx={{ paddingX: "40px", mb: "60px" }}>
        <GoodHabit
          report={report!}
        />
      </MLContainer>
    </Stack>
  )
}
export default DetailedReport;