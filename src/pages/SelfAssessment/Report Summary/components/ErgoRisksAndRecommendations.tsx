import { Box, Divider, Stack, Typography } from '@mui/material'
import MLTypography from '../../../../components/ui/MLTypography/MLTypography'
import MLButton from '../../../../components/ui/MLButton/MLButton';
import cross from "../../../../assets/reportIcons/cross.png";
import tick from "../../../../assets/reportIcons/tick.png";
import { useList } from '@refinedev/core';
import { IActionPlan, SelfAssessmentOption } from '../../Report/Report';
import AskAQuestion from '../../../Dashboard/AskAQuestion';
import { useState } from 'react';
import CustomRightArrow from '../../../../assets/icons/CustomRightArrow';

interface ErgoRisksAndRecommendationsProps {
    actionPlans: IActionPlan[];
    handleActionPlanComplete: (outcome: string, points: number, isCompleted?: boolean) => void,
    handleViewAll?: () => void
}

const ErgoRisksAndRecommendations: React.FC<ErgoRisksAndRecommendationsProps> = ({ actionPlans, handleActionPlanComplete, handleViewAll }) => {
    const [isNeedHelpModalOpened, setIsNeedHelpModalOpened] = useState<boolean>(false);
    const showActionDone = true;
    const { data: assessmentData, isLoading, error } = useList<SelfAssessmentOption>({
        resource: "self-assessment-option-configs",
        meta: {
            populate: ["optionImage"],
            fields: ["option", "optionText", "overallRisk", "riskTitle"],
        },
        filters: [
            {
                field: "option",
                operator: "in",
                value: actionPlans?.map((plan: IActionPlan) => plan.outcome) || []
            }
        ]
    });

    const findMatchingAssessment = (actionPlan: IActionPlan) => {
        return assessmentData?.data.find(
            (assessment: SelfAssessmentOption) => assessment.option === actionPlan.outcome
        );
    };

    // Function to check if an action is completed
    const isActionCompleted = (outcome: string) => {
        const actionPlan = actionPlans?.find(plan => plan.outcome === outcome);
        return actionPlan?.isCompleted || false;
    };

    // Function to handle action completion
    const onActionComplete = (outcome: string, points: number, isAlreadyCompleted: boolean) => {
        // If already completed, we'll pass negative points to remove them
        const pointsToUpdate = isAlreadyCompleted ? -points : points;
        handleActionPlanComplete(outcome, pointsToUpdate, isAlreadyCompleted);
    };

    return (
        <Stack gap="20px">
            <Stack
                direction={{ xs: "column", sm: "row" }}
                alignItems={{ xs: "flex-start", md: "center" }}
                justifyContent="space-between"
                gap={{ xs: "0px", sm: "15px" }}
            >
                <MLTypography
                    variant="h1"
                    fontSize={{ md: "32px", xs: "24px" }}
                    fontWeight={600}
                >
                    Top 3 ergo risks and recommendations
                </MLTypography>
                <MLButton
                    color="primary"
                    sx={{
                        textTransform: 'none',
                        '&:hover': {
                            backgroundColor: 'transparent',
                        },
                        marginLeft: { xs: "-8px", sm: "0px" },
                    }}
                    onClick={handleViewAll}
                    disableRipple
                >
                    View detailed report
                    <CustomRightArrow
                        sx={{
                            fontSize: "20px",
                            ml: 1
                        }}
                    />
                </MLButton>
            </Stack>
            <Stack gap="30px" >
                {actionPlans?.sort((a, b) => a.order - b.order).slice(0, 3).map((action: IActionPlan, index: number) => {
                    const matchingAssessment = findMatchingAssessment(action);
                    // consistent image dimensions for both sides
                    const imageContainerWidth = {
                        xs: "auto",
                        md: "242px",
                        lg: "280px",
                    };

                    const imageHeight = {
                        md: "182px",
                        xs: "auto"
                    };

                    return (
                        <Stack
                            key={`action-plan-${index}`}
                            sx={{
                                display: "flex",
                                gap: "30px",
                                borderRadius: "10px",
                                pt: "20px",
                                pb: "30px",
                                px: "30px",
                                border: "0.5px solid #E0E0E0",
                            }}
                        >
                            {/* Header with number and title */}
                            <Stack
                                sx={{
                                    display: "flex",
                                    gap: "22px",
                                    flexDirection: "row",
                                    alignItems: { md: "baseline", xs: "center" },
                                }}
                            >
                                <MLTypography
                                    sx={{
                                        fontFamily: "Syne",
                                        fontSize: { md: "40px", xs: "32px" },
                                        fontWeight: 700,
                                        color: "#7856FF",
                                        lineHeight: 1
                                    }}
                                >
                                    0{index + 1}
                                </MLTypography>
                                <MLTypography
                                    sx={{
                                        fontFamily: "Syne",
                                        fontSize: { sm: "24px", xs: "18px" },
                                        fontWeight: 600,
                                        lineHeight: 1
                                    }}
                                >
                                    {matchingAssessment?.riskTitle}
                                </MLTypography>
                                {action.isCompleted && (
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 34 34" fill="none">
                                        <g clipPath="url(#clip0_17600_19854)">
                                            <path d="M6.81417 18.4734L1.0625 25.5001L6.375 26.5626L8.5 32.9376L14.0817 24.0692" stroke="#333333" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                            <path d="M27.1855 18.4734L32.9371 25.5001L27.6246 26.5626L25.4996 32.9376L19.918 24.0692" stroke="#333333" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                            <path d="M17 24.4375C23.46 24.4375 28.6875 19.21 28.6875 12.75C28.6875 6.29 23.46 1.0625 17 1.0625C10.54 1.0625 5.3125 6.29 5.3125 12.75C5.3125 19.21 10.54 24.4375 17 24.4375Z" fill="#DFFF32" stroke="#333333" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                            <path d="M22.8369 8.72668L15.0877 16.4759C14.8894 16.6742 14.6202 16.7875 14.3369 16.7875C14.0536 16.7875 13.7844 16.6742 13.5861 16.4759L11.1494 14.0392" stroke="#333333" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_17600_19854">
                                                <rect width="34" height="34" fill="white" />
                                            </clipPath>
                                        </defs>
                                    </svg>
                                )}
                            </Stack>
                            
                            <Stack
                                sx={{
                                    display: "flex",
                                    gap: "22px",
                                    flexDirection: { xs: "column", sm: "row" },
                                    alignItems: "baseline",
                                }}
                            >
                                <MLTypography
                                    // Hides the text but preserves the space
                                    sx={{
                                        fontFamily: "Syne",
                                        fontSize: { md: "40px", xs: "32px" },
                                        fontWeight: 700,
                                        color: "transparent",
                                        display: { xs: "none", sm: "flex" },
                                        visibility: "hidden",
                                        lineHeight: 1
                                    }}
                                >
                                    0{index + 1}
                                </MLTypography>
                                <Stack
                                    sx={{
                                        display: "flex",
                                        gap: "30px",
                                        flexDirection: { lg: "row", md: "row", sm: "column", xs: "column" },
                                        alignItems: { sm: "center", xs: "center", md: "center" },
                                    }}
                                >
                                    <Stack
                                        direction={{ sm: "row", xs: "column" }}
                                        gap="30px"
                                    >
                                        {/* left side */}
                                        <Stack
                                            gap={{ lg: "20px", xs: "10px" }}
                                            sx={{
                                                width: imageContainerWidth,
                                                maxWidth: "300px",
                                                display: "flex",
                                                flexDirection: "column",
                                            }}
                                        >
                                            <Box
                                                sx={{
                                                    position: "relative",
                                                    width: "100%",
                                                    flexGrow: 0,
                                                }}>
                                                <Stack
                                                    sx={{
                                                        position: "absolute",
                                                        top: -10,
                                                        right: -10,
                                                        width: { md: "35px", xs: "30px" },
                                                        height: "auto",
                                                        zIndex: 10,
                                                    }}
                                                >
                                                    <img src={cross} />
                                                </Stack>
                                                <Box
                                                    component="img"
                                                    sx={{
                                                        borderStyle: "solid",
                                                        border: "0.5px solid #E0E0E0",
                                                        backgroundColor: "#fcfcfc",
                                                        borderRadius: "8px",
                                                        overflow: "hidden",
                                                        width: "100%",
                                                        height: imageHeight,
                                                        objectFit: "contain",
                                                    }}
                                                    src={matchingAssessment?.optionImage.url}
                                                    alt="actionPlanFix"
                                                />
                                            </Box>
                                            <Box
                                                sx={{
                                                    width: "100%",
                                                    minHeight: { xs: "auto", sm: "48px" },
                                                }}
                                            >
                                                <MLTypography
                                                    variant={"body1"}
                                                    fontSize={{ md: "16px", xs: "14px" }}
                                                    fontWeight={400}
                                                    lineHeight={1.2}
                                                    textAlign="left"
                                                >
                                                    {matchingAssessment?.overallRisk}
                                                </MLTypography>
                                            </Box>
                                        </Stack>

                                        {/* right side */}
                                        <Stack
                                            gap={{ lg: "20px", xs: "10px" }}
                                            sx={{
                                                width: imageContainerWidth,
                                                maxWidth: "300px",
                                                display: "flex",
                                                flexDirection: "column",
                                            }}
                                        >
                                            <Box
                                                sx={{
                                                    position: "relative",
                                                    width: "100%",
                                                }}>

                                                <Stack
                                                    sx={{
                                                        position: "absolute",
                                                        top: -10,
                                                        right: -10,
                                                        width: { md: "35px", xs: "30px" },
                                                        height: "auto",
                                                        zIndex: 10,
                                                    }}
                                                >
                                                    <img src={tick} />
                                                </Stack>
                                                <Box
                                                    component="img"
                                                    sx={{
                                                        borderStyle: "solid",
                                                        border: "0.5px solid #E0E0E0",
                                                        backgroundColor: "#fcfcfc",
                                                        borderRadius: "8px",
                                                        overflow: "hidden",
                                                        width: "100%",
                                                        height: imageHeight,
                                                        objectFit: "contain",
                                                    }}
                                                    src={action.image}
                                                    alt="actionPlanFix"
                                                />
                                            </Box>

                                            <Box
                                                sx={{
                                                    width: "100%",
                                                    minHeight: { xs: "auto", sm: "48px" },
                                                }}
                                            >
                                                <MLTypography
                                                    variant={"body1"}
                                                    fontSize={{ md: "16px", xs: "14px" }}
                                                    fontWeight={400}
                                                    lineHeight={1.2}
                                                    textAlign="left"
                                                >
                                                    {action.title}
                                                </MLTypography>
                                            </Box>
                                        </Stack>
                                    </Stack>

                                    <Divider
                                        orientation="horizontal"
                                        flexItem
                                    />

                                    {/* Action buttons section */}
                                    <Stack
                                        width={{ md: "25%", sm: "100%" }}
                                        spacing={2}
                                        alignItems={{ xs: "center", sm: "center" }}
                                        justifyContent="space-between"
                                    >
                                        <MLTypography
                                            variant="body1"
                                            fontSize="14px"
                                            fontWeight={400}
                                            lineHeight={1.2}
                                        >
                                            Add{" "}
                                            <span
                                                style={{
                                                    fontWeight: 600,
                                                    fontSize: "16px",
                                                    lineHeight: 1.2,
                                                    color: "#31C100",
                                                }}
                                            >
                                                {action.points} points
                                            </span>
                                            {" "}to your ergo score
                                        </MLTypography>

                                        <Stack width={{ sm: "auto", md: "100%" }}>
                                            {!isActionCompleted(action.outcome) ? (
                                                <MLButton
                                                    variant="contained"
                                                    color="secondary"
                                                    onClick={() => onActionComplete(action.outcome, action.points, false)}
                                                >
                                                    Mark As Done
                                                </MLButton>
                                            ) : (
                                                <MLButton
                                                    variant="outlined"
                                                    color="secondary"
                                                    sx={{
                                                        backgroundColor: "#98E080",
                                                        border: "1px solid black",
                                                        color: "black",
                                                        '&:hover': {
                                                            backgroundColor: "#98E080",
                                                            border: "1px solid black",
                                                            color: "black"
                                                        }
                                                    }}
                                                >
                                                    Completed
                                                </MLButton>
                                            )}
                                        </Stack>

                                        {isActionCompleted(action.outcome) && (
                                            <MLButton
                                                variant="text"
                                                color="primary"
                                                onClick={() => onActionComplete(action.outcome, action.points, true)}
                                                sx={{
                                                    textTransform: "none",
                                                    fontSize: "14px",
                                                    padding: { xs: "8px 16px", sm: 0 },
                                                    fontWeight: 600,
                                                    '&:hover': {
                                                        backgroundColor: "transparent",
                                                        color: "#5E3DCF"
                                                    }
                                                }}
                                            >
                                                Reset this task
                                            </MLButton>
                                        )}

                                        <MLButton
                                            variant="text"
                                            color="primary"
                                            sx={{
                                                textTransform: "none",
                                                fontSize: "14px",
                                                padding: { xs: "8px 16px", sm: 0 },
                                                fontWeight: 600,
                                                '&:hover': {
                                                    backgroundColor: "transparent",
                                                    color: "#5E3DCF"
                                                }
                                            }}
                                            onClick={() => setIsNeedHelpModalOpened(true)}
                                        >
                                            Need help?
                                        </MLButton>
                                    </Stack>
                                </Stack>
                            </Stack>
                        </Stack>
                    );
                })}
            </Stack>
            <AskAQuestion
                openAskAQuestionModal={isNeedHelpModalOpened}
                handleClose={() => setIsNeedHelpModalOpened(false)}
                isActionPlan={true}
            />
        </Stack >
    )
}

export default ErgoRisksAndRecommendations;
