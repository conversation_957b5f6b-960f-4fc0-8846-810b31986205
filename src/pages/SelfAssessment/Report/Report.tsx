interface ICondition {
  order: number;
  bodyPart: string;
  conditionName: string;
  description: string;
  conditionImage: string | null;
  altConditionImage: string | null;
}

export interface IProduct {
  id: number;
  name: string;
  description: string;
  price: number | null | undefined;
  image: { id: string; url: string };
  url: string;
  recommendationType: "Product" | "DIY"
}

interface IRecommendation {
  title: string;
  text: string | string[];
  image: string;
  outcome: string;
  optionConfig: {
    optionImage: string;
    overallRisk: string;
    riskTitle: string;
  }
}

export interface BadHabit {
  habit: string;
  optionConfig: {
    optionImage: string;
    overallRisk: string;
    riskTitle: string;
  }
  postureScore: number;
  affectedBodyPart: string;
  bodyPartOrder: number;
  canHaveConditions: {
    bodyPart: string;
    condition: string;
    description: string | null;
    riskLevel: string;
    conditionImage: { url: string };
    altConditionImage: { url: string }
  }[]
  title: string;
  currentHabit: string;
  currentHabitImage: string;
  recommendation: IRecommendation;
  productRecommendations: {
    [key: string]: IProduct[]
  }
  minorRisk: Array<{ [key: string]: ICondition }>;
  majorRisk: Array<{ [key: string]: ICondition }>;
}

export interface ICalcHabitsScore {
  habit: string;
  impactFactorPoints: number;
  numOfFactors: number;
  isGoodHabit: boolean;
  maxPossiblePoints: number;
}

export interface IReport {
  calcHabitsScore: ICalcHabitsScore[];
  badHabits: BadHabit[];
  ergoPostureScore: number;
  finalScore: number;
  goodHabits: GoodHabit[];
  maxScore: number;
  additionalActionPlans: any[];
}

export interface GoodHabit {
  title: string;
  text: string;
  image: string;
}

export interface RiskInfo {
  riskLevel: "medium" | "high" | "low";
  potentialCondition: ICondition;
  causedBys: {
    title: string; text: string | string[]; image: string, outcome: string, currentHabitReportUserImage?: string; currentHabitReportUserAnnotatedImage?: string;
  }[];
  recommendations: IRecommendation[];
  productRecommendations: IProduct[];
  hasDiscomfortSelected: boolean;
  hasConditionSelected: string[];
}

export interface IReportData {
  ergoPostureScore: number;
  achievedActionScore: number;
  backBodyImage: {
    id: number;
    url: string;
    base64?: string;
  };
  frontBodyImage: {
    id: number;
    url: string;
    base64?: string;
  };
  username: string;
  potentialRiskPart: {
    [bodyPart: string]: RiskInfo
  };
  goodHabits: GoodHabit[];
  actionPlans: IActionPlan[];
  mergedProducts: IProduct[];
  ergonomicScoreText: string;
  reportGeneratedBodyParts: any;
  uploadedImages?: string[];
}

export interface IGeneratedReport {
  id: number;
  generatedReport: IReportData;
  generatedPdfUrl: string;
}

export interface IActionPlan {
  title: string;
  text: string | string[];
  image: string;
  points: number;
  outcome: string;
  isCompleted: boolean;
  order: number;
  optionConfig: {
    optionImage: string;
    overallRisk: string;
    riskTitle: string;
  }
}

export type RiskLevelType = 'low' | 'medium' | 'high';
export interface SelfAssessmentOption {
  option: string;
  optionText: string;
  overallRisk: string;
  riskTitle: string;
  optionImage: { url: string };
}

export enum RiskLevel {
  "high",
  "medium",
  "low",
}