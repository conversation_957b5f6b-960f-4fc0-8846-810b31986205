import needHelpIcon from "../../../assets/reportIcons/needHelpIcon.svg";
import tick from "../../../assets/reportIcons/tick.png";
import bookSessionIcon from "../../../assets/reportIcons/bookSessionIcon.svg"
import ConditionToolTipIcon from '../../../assets/reportIcons/ConditionToolTipIcon';
import { Close, Edit, Save } from "@mui/icons-material";
import MLButton from '../../../components/ui/MLButton/MLButton';
import MLInputbox from '../../../components/ui/MLInputbox/MLInputbox';
import MLTypography from "../../../components/ui/MLTypography/MLTypography";
import GoodHabitItem from "./components/GoodHabitItem";
import {
  GoodHabit,
  IActionPlan,
  IProduct,
  RiskInfo,
} from "./Report";
import ResultItem from "./components/ResultItem";
import {
  ChevronRight,
  ExpandLess,
  ExpandMore,
  WarningAmber,
} from "@mui/icons-material";
import {
  Alert,
  Box,
  Button,
  Collapse,
  Container,
  Divider,
  Grid,
  Stack,
  StackProps,
  Tooltip,
  IconButton
} from "@mui/material";
import React, { useState } from "react";
import MLContainer from "../../../components/ui/MLMaxWidthContainer/MLMaxWidthContainer";
import { useNavigate } from "react-router-dom";

interface ProductItemProps {
  product: IProduct;
  showProductPrice: boolean;
  enableProductLink: boolean;
}
const ProductItem = ({
  product,
  showProductPrice,
  enableProductLink,
}: ProductItemProps) => {
  return (
    <Stack direction={"row"} gap={"20px"}>
      <Stack>
        <Box
          component="img"
          sx={{
            backgroundColor: "#fcfcfc",
            borderRadius: "8px",
            width: "105px",
            height: "115px",
          }}
          //src={product.image.url || ""}
          src={product.image ? product.image.url : ""}
          alt="Product image"
        />
      </Stack>
      <Stack
        direction={"row"}
        width={"100%"}
        sx={{
          justifyContent: "space-between",
        }}
      >
        <Stack direction={"column"}>
          <Stack gap={"8px"}>
            <Stack direction={"column"} gap={"4px"}
              sx={{
              }}
            >
              <MLTypography
                variant="body1"
                fontSize={{ md: "20px", xs: "15px" }}
                fontWeight={600}
                lineHeight={1}
              >
                {product.name}
              </MLTypography>
              {/* <Stack direction={"row"}>
                <Stack
                  sx={{
                    border: "1px solid #333333",
                    paddingX: "4px",
                    paddingY: "2px",
                    borderRadius: "5px",
                  }}
                >
                  <MLTypography
                    variant="body1"
                    fontSize={"16px"}
                    fontWeight={400}
                    lineHeight={1}
                  >
                    {product.recommendationType}
                  </MLTypography>
                </Stack>
              </Stack> */}
            </Stack>
            <MLTypography
              variant="body1"
              fontSize={"16px"}
              fontWeight={400}
              lineHeight={1}
              sx={{ wordBreak: "break-word" }}
            >
              {product.description}
            </MLTypography>
          </Stack>
          {(showProductPrice && product.price !== null && product.price !== undefined && product.price !== 0 && product.recommendationType !== "DIY") && (
            <MLTypography
              variant="body1"
              fontSize={{ md: "20px", xs: "15px" }}
              fontWeight={600}
              lineHeight={1.2}
            >
              ${product.price}
            </MLTypography>
          )}
          {(enableProductLink && product.price !== null && product.price !== undefined && product.recommendationType !== "DIY") && (
            <Stack marginTop={"10px"} direction={"row"}>
              <Button
                variant="outlined"
                sx={{
                  padding: "8px",
                }}
                href={product.url}
                target="_blank"
              >
                BUY ITEM
                <ChevronRight />
              </Button>
            </Stack>
          )}
        </Stack>
      </Stack>
    </Stack>
  );
};

// text content for section header; e.g "Lower Back, You have a ..."
interface BodyPartConditionProps {
  bodyPart: string;
  value: RiskInfo;
  index: number;
  isMoreThanTwoCauses: boolean;
  isLeftBorder: boolean;
  isVerySmallScreen: boolean;
}
const BodyPartCondition = ({
  bodyPart,
  value,
  index,
  isMoreThanTwoCauses,
  isLeftBorder,
  isVerySmallScreen
}: BodyPartConditionProps) => {
  return (
    <Stack>
      <Stack
        id={bodyPart}
        //marginBottom={"20px"}
        marginBottom={{ md: "20px", xs: "10px" }}
      >
        <MLTypography
          variant="h1"
          fontSize={{ md: "88px", xs: "60px" }}
          fontWeight={400}
          lineHeight={0.7}
          color={"#6b49f2"}
          //marginBottom={"40px"}
          marginBottom={{ md: "40px", xs: "20px" }}
        >
          {(index + 1).toString().padStart(2, "0")}.
        </MLTypography>
        <MLTypography
          variant="h1"
          fontSize={{ md: "56px", xs: "35px" }}
          //fontSize={"56px"}
          fontWeight={600}
        >
          {bodyPart}
        </MLTypography>
      </Stack>
      <Stack gap={"4px"}>
        <Stack gap={"4px"}>
          <MLTypography
            width={"100%"}
            variant="body1"
            fontSize={{ md: "20px", xs: "17px" }}
            fontWeight={400}
            lineHeight={1.2}
          >
            You have{" "}
          </MLTypography>
          <Stack
            direction={{ xs: isVerySmallScreen ? "row" : "column", sm: "row" }}
            width={"100%"}
            alignItems={{ xs: isVerySmallScreen ? "center" : "flex-start", sm: "center" }}
            gap={"4px"}
          >
            <Stack
              direction="row"
              sx={{
                backgroundColor:
                  value.riskLevel === "high"
                    ? "#d03f43"
                    : value.riskLevel === "low"
                      ? "#FFC700"
                      : "#FF7A00",
                borderRadius: "34px",
                padding: "8px",
              }}
            >
              <MLTypography
                variant="body1"
                fontSize={{ md: "14px", xs: "12px" }}
                fontWeight={500}
                lineHeight={1}
                sx={{
                  letterSpacing: "1px",
                  color:
                    value.riskLevel === "high"
                      ? "white"
                      : value.riskLevel === "low"
                        ? "black"
                        : "black",
                }}
                textAlign={"center"}
              >
                {(value.riskLevel[0] ?? "").toUpperCase() + value.riskLevel.slice(1) + " Risk"}
              </MLTypography>
            </Stack>
            <MLTypography
              variant="body1"
              fontSize={{ md: "20px", xs: "17px" }}
              fontWeight={400}
              lineHeight={1.2}
            >
              {" "}of developing{" "}
            </MLTypography>
          </Stack>
        </Stack>

        {/* <MLTypography
          width={{
            xs: "100%",
            sm: isMoreThanTwoCauses ? "80%" : "100%",
          }}
          variant="body1"
          fontSize={{ md: "20px", xs: "17px" }}
          fontWeight={400}
          lineHeight={1.2}
        >
          Your current posture put you at{" "}
          <span
            style={{
              fontWeight: 700,
              lineHeight: 1,
              letterSpacing: "1px",
              //color: value.riskLevel === "high" ? "#d03f43" : "#eab34b"
              color:
                value.riskLevel === "high"
                  ? "#d03f43"
                  : value.riskLevel === "low"
                    ? "#FFFF32"
                    : "#eab34b",
            }}
          >
            {value.riskLevel.toUpperCase() + " RISK"}
          </span>{" "}
          of developing{" "}
        </MLTypography> */}
        <Stack direction={"column"} gap={"10px"}>
          <Stack
            direction={"row"}
            gap={"10px"}
            sx={{
              alignItems: "center",
            }}
          >
            <MLTypography fontSize={"25px"} fontWeight={600} lineHeight={1.2}>
              {value.potentialCondition.conditionName}
            </MLTypography>
            {value.potentialCondition.description ? (
              <>
                <Tooltip
                  title={
                    <div>
                      <strong>{value.potentialCondition.conditionName}</strong> -{" "}
                      {value.potentialCondition.description}
                    </div>
                  }
                  placement={isLeftBorder ? "right-end" : "left-end"}
                  slotProps={{
                    tooltip: {
                      sx: {
                        bgcolor: "#E4E4E4",
                        color: "rgba(0, 0, 0, 0.87)",
                        fontSize: "12px",
                        p: "15px",
                        borderRadius: "10px",
                      },
                    },
                  }}
                  sx={{
                    display: { xs: "none", sm: "flex" }
                  }}
                >
                  <Stack>
                    <ConditionToolTipIcon />
                  </Stack>
                </Tooltip>
                <Tooltip
                  enterTouchDelay={1}
                  title={
                    <div>
                      <strong>{value.potentialCondition.conditionName}</strong> -{" "}
                      {value.potentialCondition.description}
                    </div>
                  }
                  placement={"top"}
                  slotProps={{
                    tooltip: {
                      sx: {
                        bgcolor: "#E4E4E4",
                        color: "rgba(0, 0, 0, 0.87)",
                        fontSize: "12px",
                        p: "15px",
                        borderRadius: "10px",
                      },
                    },
                  }}
                  sx={{
                    display: { xs: "flex", sm: "none" }
                  }}
                >
                  <Stack>
                    <ConditionToolTipIcon />
                  </Stack>
                </Tooltip>
              </>
            ) : (
              <></>
            )}
          </Stack>
          {value.hasDiscomfortSelected ||
            (value.hasConditionSelected || []).length != 0 ? (
            <Alert
              variant="filled"
              icon={false}
              sx={{
                borderColor: "#FF6E6E",
                backgroundColor: "#FF6E6E",
                color: "white",
                borderWidth: "1px",
                borderStyle: "solid",
                borderRadius: "10px",
              }}
            >
              <Stack
                direction={"row"}
                gap={"10px"}
                sx={{
                  alignItems: "center",
                }}
              >
                <WarningAmber />
                <MLTypography
                  variant={"body1"}
                  fontSize={"12px"}
                  fontWeight={500}
                  lineHeight={1.2}
                >
                  {value.hasDiscomfortSelected &&
                    (value.hasConditionSelected || []).length >= 1
                    ? `Your reported ${bodyPart} discomfort and ${value.hasConditionSelected.join(
                      ", ",
                    )} condition is likely caused by your ergo risk habit.`
                    : value.hasDiscomfortSelected &&
                      (value.hasConditionSelected || []).length == 0
                      ? `Your reported ${bodyPart} discomfort is likely caused by your ergo risk habit.`
                      : (value.hasConditionSelected || []).length > 0
                        ? `Your reported ${value.hasConditionSelected.join(
                          ", ",
                        )} condition is likely caused by your ergo risk habit.`
                        : ""}
                </MLTypography>
              </Stack>
            </Alert>
          ) : (
            <></>
          )}
        </Stack>
      </Stack>
    </Stack>
  );
};

// sidebar highlight of body section
interface SideHighlightProps {
  isLeftBorder: boolean;
  color?: string;
  width?: string | object,
  onlyLeftSide?: boolean;
}
export const SideHighlight = ({
  isLeftBorder,
  color = "#ddd2fd",
  width = { xs: "10px", md: "20px", lg: "25px", xl: "35px", },
  onlyLeftSide = false,
}: SideHighlightProps) => {
  return (
    <Box
      sx={{
        position: "absolute",
        width: width,
        //right: isLeftBorder ? undefined : 0,
        right: onlyLeftSide ? "auto" : isLeftBorder ? { xs: 0, md: 0, lg: "auto" } : { xs: "auto", md: "auto", lg: 0 },
        height: "100%",
        backgroundColor: color,
      }}
    />
  );
}

const SectionTitle = ({ title }: { title: string }) => (
  <MLTypography
    variant="body1"
    fontSize={{ md: "24px", xs: "15px" }}
    fontWeight={600}
    lineHeight={1.2}
    marginBottom={{ md: "24px", xs: "0px" }}
  >
    {title}
  </MLTypography>
);

// start here
// for screens smaller than 320px
interface VerySmallScreenProps {
  value: RiskInfo;
  isDetailed: boolean;
  handleEditCause?: ((index: string, updatedTitle: string, updatedText: string | string[]) => void);
  handleEditRecommendation?: ((index: string, updatedTitle: string, updatedText: string | string[]) => void);
  mediaAttachments?: any[];
  caseId?: string | number;
  reportId?: number;
  bodyPart?: string;
  onUpdateImage?: (imageUrl: string, index: number) => void;
  showEditables: boolean;
  showProductPrice: boolean;
  enableProductLink: boolean;
}
const VerySmallScreen = ({
  value,
  isDetailed,
  handleEditCause,
  handleEditRecommendation,
  mediaAttachments,
  caseId,
  reportId,
  bodyPart,
  onUpdateImage,
  showEditables,
  showProductPrice,
  enableProductLink,
}: VerySmallScreenProps) => {
  return (
    <Grid item xs={12}>
      <Stack spacing={3}>
        <Stack>
          <SectionTitle title="ERGO RISKS" />
          <Stack gap={4}>
            {value.causedBys.map((causes, index) => (
              <ResultItem
                key={index + causes.title}
                image={causes.image}
                title={causes.title}
                text={causes.text}
                isCross={true}
                index={index}
                isDetailed={isDetailed}
                mediaAttachments={mediaAttachments}
                caseId={caseId}
                reportId={reportId}
                bodyPart={bodyPart}
                onUpdateImage={(newImage) => onUpdateImage?.(newImage, index)}
                outcome={causes.outcome}
                onSave={handleEditCause}
                showEditables={showEditables}
              />
            ))}
          </Stack>
        </Stack>
        <Stack>
          <SectionTitle title="RECOMMENDATIONS" />
          <Stack gap={4}>
            {value.recommendations.map((recommendation, index) => (
              <ResultItem
                key={index + recommendation.title}
                image={recommendation.image}
                title={recommendation.title}
                text={recommendation.text}
                isCross={false}
                index={index}
                isDetailed={isDetailed}
                outcome={recommendation.outcome}
                onSave={handleEditRecommendation}
                mediaAttachments={mediaAttachments}
                caseId={caseId}
                showEditables={showEditables}
              />
            ))}
          </Stack>
        </Stack>
        {value.productRecommendations.length == 0 ? (
          <></>
        ) : (
          <Stack>
            <SectionTitle title="PRODUCT SUGGESTIONS" />
            {value.productRecommendations.map((product, index) => (
              <Stack key={index + product.name}>
                <ProductItem
                  product={product}
                  showProductPrice={showProductPrice}
                  enableProductLink={enableProductLink}
                />
              </Stack>
            ))}
          </Stack>
        )}
      </Stack>
    </Grid>
  );
};

interface DetailedViewProps {
  value: RiskInfo;
  handleEditCause: any;
  handleEditRecommendation: any;
  mediaAttachments?: any[];
  caseId?: string | number;
  onUpdateImage?: (imageUrl: string, index: number) => void;
  reportId?: number;
  bodyPart?: string;
  showEditables: boolean;
  showProductPrice: boolean;
  enableProductLink: boolean;
}
const DetailedView = ({
  value,
  handleEditCause,
  handleEditRecommendation,
  mediaAttachments,
  caseId,
  onUpdateImage,
  reportId,
  bodyPart,
  showEditables,
  showProductPrice,
  enableProductLink,
}: DetailedViewProps) => {
  return (
    <Stack gap={4}>
      <Stack>
        <Stack marginBottom={"16px"}>
          <SectionTitle title="ERGO RISKS" />
        </Stack>
        <Grid container spacing={3}>
          {value.causedBys.map((causes, index) => (
            <Grid key={index + causes.title} item xs={12}>
              <ResultItem
                image={causes.image}
                title={causes.title}
                text={causes.text}
                isCross={true}
                index={index}
                outcome={causes.outcome}
                onSave={handleEditCause}
                mediaAttachments={mediaAttachments}
                caseId={caseId}
                reportId={reportId}
                bodyPart={bodyPart}
                onUpdateImage={(newImage) => onUpdateImage?.(newImage, index)}
              />
            </Grid>
          ))}
        </Grid>
      </Stack>

      <Stack>
        <Stack marginBottom={"16px"}>
          <SectionTitle title="RECOMMENDATIONS" />
        </Stack>
        <Grid container spacing={3}>
          {value.recommendations.map((recommendation, index) => (
            <Grid key={index + recommendation.title} item xs={12}>
              <ResultItem
                image={recommendation.image}
                title={recommendation.title}
                text={recommendation.text}
                isCross={false}
                index={index}
                outcome={recommendation.outcome}
                onSave={handleEditRecommendation}
                showEditables={showEditables}
              />
            </Grid>
          ))}
        </Grid>
      </Stack>

      <Stack>
        {value.productRecommendations.length == 0 ? (
          <></>
        ) : (
          <Stack marginBottom={"16px"}>
            <SectionTitle title="PRODUCT SUGGESTIONS" />
          </Stack>
        )}
        <Grid container spacing={3}>
          {value.productRecommendations.map((product, index) => (
            <Grid key={index + product.name} item xs={12}>
              <ProductItem
                product={product}
                showProductPrice={showProductPrice}
                enableProductLink={enableProductLink}
              />
            </Grid>
          ))}
        </Grid>
      </Stack>
    </Stack>
  );
};

interface SnapshotViewProps {
  value: RiskInfo;
  isDetailed: boolean;
  handleEditCause: any;
  handleEditRecommendation: any;
  mediaAttachments?: any[];
  caseId?: string | number;
  reportId?: number;
  bodyPart?: string;
  onUpdateImage?: (imageUrl: string, index: number) => void;
  showEditables: boolean;
  showProductPrice: boolean;
  enableProductLink: boolean;
}
const SnapshotView = ({
  value,
  isDetailed,
  handleEditCause,
  handleEditRecommendation,
  mediaAttachments,
  caseId,
  reportId,
  bodyPart,
  onUpdateImage,
  showEditables,
  showProductPrice,
  enableProductLink,
}: SnapshotViewProps) => {
  return (
    <Stack gap={4}>
      <Grid container spacing={{ xs: 2, md: 4 }} marginBottom={"20px"}>
        <Grid item xs={6}>
          <SectionTitle title="ERGO RISKS" />
        </Grid>
        <Grid item xs={6}>
          <SectionTitle title="RECOMMENDATIONS" />
        </Grid>
        {value.causedBys.map((causes, index) => (
          <React.Fragment key={causes.title + index}>
            <Grid item xs={6}>
              <ResultItem
                image={causes.image}
                title={causes.title}
                text={causes.text}
                isCross={true}
                index={index}
                isDetailed={isDetailed}
                outcome={causes.outcome}
                onSave={handleEditCause}
                mediaAttachments={mediaAttachments}
                caseId={caseId}
                reportId={reportId}
                bodyPart={bodyPart}
                onUpdateImage={(newImage) => onUpdateImage?.(newImage, index)}
                showEditables={showEditables}
              />
            </Grid>
            <Grid item xs={6}>
              <ResultItem
                image={value.recommendations[index].image}
                title={value.recommendations[index].title}
                text={value.recommendations[index].text}
                isCross={false}
                index={index}
                isDetailed={isDetailed}
                outcome={value.recommendations[index].outcome}
                onSave={handleEditRecommendation}
                mediaAttachments={mediaAttachments}
                caseId={caseId}
                showEditables={showEditables}
              />
            </Grid>
          </React.Fragment>
        ))}
      </Grid>
      <Stack>
        {value.productRecommendations.length == 0 ? (
          <></>
        ) : (
          <Stack marginBottom={"16px"}>
            <SectionTitle title="PRODUCT SUGGESTIONS" />
          </Stack>
        )}
        <Grid container spacing={3}>
          {value.productRecommendations.map((product, index) => (
            <Grid key={index + product.name} item xs={12}>
              <ProductItem
                product={product}
                showProductPrice={showProductPrice}
                enableProductLink={enableProductLink}
              />
            </Grid>
          ))}
        </Grid>
      </Stack>
    </Stack>
  );
};

interface CollapseButtonProps {
  bodyPart: string,
  index: number,
  isCollapsedSections: { [sectionKey: string]: boolean },
  handleCollapseToggle: (section: string) => void,
}
const CollapseButton = ({
  bodyPart,
  index,
  isCollapsedSections,
  handleCollapseToggle,
}: CollapseButtonProps) => {
  return (
    <Stack
      direction={"row"}
      gap={"7px"}
      sx={{
        alignItems: "center",
      }}
      onClick={() => handleCollapseToggle(bodyPart + index)}
    >
      <Box
        sx={{
          backgroundColor: "#7856FF",
          borderRadius: "50px",
          width: "46px",
          height: "46px",
          zIndex: 99
        }}
      >
        <Stack sx={{
          height: "100%",
          justifyContent: "center",
          alignItems: "center",
        }}>
          {isCollapsedSections[bodyPart + index] ? (
            <ExpandLess sx={{ color: "white" }} />
          ) : (
            <ExpandMore sx={{ color: "white" }} />
          )}
        </Stack>
      </Box>
      <MLTypography variant='body1' fontSize={"16px"} fontWeight={500} lineHeight={0.7}>
        {isCollapsedSections[bodyPart + index] ? "View less" : "View more"}
      </MLTypography>
    </Stack>
  );
}

interface ContentContainerProps extends StackProps {
  children: React.ReactNode;
  isLeftBorder: boolean;
  backgroundColor?: string;
  hasSideHighlight?: boolean;
  containerPaddingTop?: string;
  containerPaddingBottom?: string;
  hasContainer?: boolean;
}

const ContentContainer = ({
  children,
  isLeftBorder,
  backgroundColor = "",
  hasSideHighlight = true,
  containerPaddingTop = "40px",
  containerPaddingBottom = "40px",
  hasContainer = true,
  ...stackProps
}: ContentContainerProps) => {
  if (hasContainer) {
    return (
      <Stack
        {...stackProps}
        sx={{
          position: "relative",
          backgroundColor: backgroundColor,
        }}
      >
        {hasSideHighlight ? <SideHighlight isLeftBorder={isLeftBorder} /> : <></>}
        <Stack
          sx={{
            paddingTop: containerPaddingTop,
            paddingBottom: containerPaddingBottom,

          }}
        >
          {children}
        </Stack>
      </Stack>
    );
  } else {
    return (
      <Stack>
        {children}
      </Stack>
    )
  }
};

// start here
// for 3 or more causes
interface LayoutAProps {
  bodyPart: string;
  value: RiskInfo;
  index: number;
  isLeftBorder: boolean;
  isDetailed: boolean;
  isCollapsedSections: { [sectionKey: string]: boolean };
  isVerySmallScreen: boolean;
  handleCollapseToggle: (section: string) => void;
  handleEditCause?: (outcome: string, updatedTitle: string, updatedText: string | string[]) => void;
  handleEditRecommendation?: (outcome: string, updatedTitle: string, updatedText: string | string[]) => void;
  mediaAttachments?: any[]; // Add this
  caseId?: string | number; // Add this
  reportId?: number;
  onUpdateImage?: (imageUrl: string, index: number) => void; // Add this
  showEditables?: boolean;
  showProductPrice: boolean;
  enableProductLink: boolean;
}
export const LayoutA = ({
  bodyPart,
  value,
  index,
  isLeftBorder,
  isDetailed,
  isCollapsedSections,
  isVerySmallScreen,
  handleCollapseToggle,
  handleEditCause,
  handleEditRecommendation,
  mediaAttachments,
  caseId,
  reportId,
  onUpdateImage,
  showEditables = false,
  showProductPrice,
  enableProductLink,
}: LayoutAProps) => {
  return (
    <>
      {/* adaptive view for device width > 900px */}
      <ContentContainer
        isLeftBorder={isLeftBorder}
        display={{ xs: "none", md: "block" }}
      >
        <MLContainer sx={{ paddingX: "40px" }}>
          <Grid
            container
            direction={isLeftBorder ? "row" : "row-reverse"}
            marginBottom={"30px"}
            spacing={4}
          >
            <Grid item xs={4}>
              <BodyPartCondition
                bodyPart={bodyPart}
                value={value}
                index={index}
                isMoreThanTwoCauses={true}
                isLeftBorder={isLeftBorder}
                isVerySmallScreen={isVerySmallScreen}
              />
              <Stack>
                <Box
                  sx={{
                    position: "relative",
                    height: "100%",
                  }}
                >
                  <Box
                    component="img"
                    sx={{
                      width: "100%",
                      height: bodyPart === "Lower Back" ? "60%" : "70%",
                    }}
                    src={value.potentialCondition.conditionImage || ""}
                    alt="Condition"
                  />
                </Box>
              </Stack>
            </Grid>
            <Grid item xs={8}>
              <Grid container spacing={4} marginBottom={"20px"}>
                <Grid item xs={6}>
                  <MLTypography
                    variant="body1"
                    fontSize={{ md: "24px", xs: "15px" }}
                    fontWeight={600}
                    lineHeight={1.2}
                  >
                    ERGO RISKS
                  </MLTypography>
                </Grid>
                <Grid item xs={6}>
                  <MLTypography
                    variant="body1"
                    fontSize={{ md: "24px", xs: "15px" }}
                    fontWeight={600}
                    lineHeight={1.2}
                  >
                    RECOMMENDATIONS
                  </MLTypography>
                </Grid>
              </Grid>
              <Stack gap={"32px"}>
                {value.causedBys.map((causes, index) => (
                  <Grid key={index + causes.title} container spacing={4}>
                    <Grid item xs={6}>
                      {/* ergo risk */}

                      <ResultItem
                        image={causes.image}
                        title={causes.title}
                        text={causes.text}
                        isCross={true}
                        index={index}
                        isRow={false}
                        outcome={causes.outcome}
                        onSave={handleEditCause}
                        mediaAttachments={mediaAttachments} // Add this
                        caseId={caseId} // Add this
                        reportId={reportId}
                        bodyPart={bodyPart}
                        onUpdateImage={(newImage) =>
                          onUpdateImage?.(newImage, index)
                        } // Add this
                        showEditables={showEditables}
                        currentHabitReportUserImage={causes.currentHabitReportUserImage}
                        currentHabitReportUserAnnotatedImage={causes.currentHabitReportUserAnnotatedImage}
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <ResultItem
                        image={value.recommendations[index].image}
                        title={value.recommendations[index].title}
                        text={value.recommendations[index].text}
                        isCross={false}
                        index={index}
                        isRow={false}
                        outcome={value.recommendations[index].outcome}
                        onSave={handleEditRecommendation}
                        showEditables={showEditables}
                      />
                    </Grid>
                  </Grid>
                ))}

                <Stack>
                  <Grid container spacing={4} marginBottom={"20px"}>
                    {value.productRecommendations.length == 0 ? (
                      <></>
                    ) : (
                      <Grid item xs={6}>
                        <MLTypography
                          variant="body1"
                          fontSize={{ md: "24px", xs: "15px" }}
                          fontWeight={600}
                          lineHeight={1.2}
                        >
                          PRODUCT SUGGESTIONS
                        </MLTypography>
                      </Grid>
                    )}
                  </Grid>
                  <Grid container spacing={3}>
                    {value.productRecommendations.map((product, index) => (
                      <Grid key={index + product.name} item xs={6}>
                        <ProductItem
                          product={product}
                          showProductPrice={showProductPrice}
                          enableProductLink={enableProductLink}
                        />
                      </Grid>
                    ))}
                  </Grid>
                </Stack>
              </Stack>
            </Grid>
          </Grid>
        </MLContainer>
      </ContentContainer>
      {/* adaptive view for device width > 900px */}

      {/* adaptive view for device width < 900px */}
      <ContentContainer
        isLeftBorder={isLeftBorder}
        containerPaddingTop={"20px"}
        containerPaddingBottom={"20px"}
        display={{ xs: "block", md: "none" }}
      >
        <MLContainer>
          {isDetailed ? (
            // detailed view
            <>
              <Grid
                container
                direction={isLeftBorder ? "row" : "row-reverse"}
                spacing={4}
                marginBottom={"32px"}
              >
                <Grid item xs={12}>
                  <LayoutBHeaderSection
                    bodyPart={bodyPart}
                    value={value}
                    index={index}
                    isRow={true}
                    numOfCauses={1}
                    isVerySmallScreen={isVerySmallScreen}
                  />
                </Grid>
              </Grid>
              <Grid container marginBottom={"20px"}>
                <Grid item xs={12}>
                  <CollapseButton
                    bodyPart={bodyPart}
                    index={index}
                    isCollapsedSections={isCollapsedSections}
                    handleCollapseToggle={handleCollapseToggle}
                  />
                </Grid>
              </Grid>
              <Collapse in={isCollapsedSections[bodyPart + index]}>
                <DetailedView
                  value={value}
                  handleEditCause={handleEditCause}
                  handleEditRecommendation={handleEditRecommendation}
                  showEditables={showEditables}
                  showProductPrice={showProductPrice}
                  enableProductLink={enableProductLink}
                />
              </Collapse>
            </>
          ) : (
            // detailed view
            // snapshot view
            <>
              <Grid
                container
                direction={isLeftBorder ? "row" : "row-reverse"}
                spacing={4}
                marginBottom={"16px"}
              >
                <Grid item xs={12}>
                  <LayoutBHeaderSection
                    bodyPart={bodyPart}
                    value={value}
                    index={index}
                    isRow={true}
                    numOfCauses={1}
                    isVerySmallScreen={isVerySmallScreen}
                  />
                </Grid>
              </Grid>
              <Grid container marginBottom={"20px"}>
                <Grid item xs={12}>
                  <CollapseButton
                    bodyPart={bodyPart}
                    index={index}
                    isCollapsedSections={isCollapsedSections}
                    handleCollapseToggle={handleCollapseToggle}
                  />
                </Grid>
              </Grid>

              <Collapse in={isCollapsedSections[bodyPart + index]}>
                {isVerySmallScreen ? <VerySmallScreen
                  value={value}
                  isDetailed={isDetailed}
                  handleEditCause={handleEditCause}
                  handleEditRecommendation={handleEditRecommendation}
                  showEditables={showEditables}
                  showProductPrice={showProductPrice}
                  enableProductLink={enableProductLink}
                /> : <SnapshotView
                  value={value}
                  isDetailed={isDetailed}
                  handleEditCause={handleEditCause}
                  handleEditRecommendation={handleEditRecommendation}
                  showEditables={showEditables}
                  showProductPrice={showProductPrice}
                  enableProductLink={enableProductLink}
                />
                }
              </Collapse>
            </>
            // snapshot view
          )}
        </MLContainer>
      </ContentContainer>
      <Stack
        display={{ xs: "block", md: "none" }}
        sx={{
          position: "relative",
        }}
      >
      </Stack>
      {/* adaptive view for device width < 900px */}
    </>
  );
};

// header section for layout b; containing condition image and text
interface LayoutBHeaderSectionProps {
  bodyPart: string;
  value: RiskInfo;
  index: number;
  isRow?: boolean;
  numOfCauses: number;
  isVerySmallScreen: boolean;
}
const LayoutBHeaderSection = ({
  bodyPart,
  value,
  index,
  isRow = false,
  numOfCauses,
  isVerySmallScreen,
}: LayoutBHeaderSectionProps) => {
  const isLeftBorder = index % 2 === 0;
  return (
    <Stack
      direction={isVerySmallScreen ? "column" :
        isRow ? { xl: isLeftBorder ? "row" : "row-reverse", lg: "column", xs: isLeftBorder ? "row" : "row-reverse" }
          : { lg: "column", xs: isLeftBorder ? "row" : "row-reverse" }}
      sx={{ alignItems: "center" }}
      gap={isRow ? "30px" : "20px"}
    >
      <Stack width={isVerySmallScreen ? "100%" : { md: "100%", xs: "50%" }}>
        <BodyPartCondition
          bodyPart={bodyPart}
          value={value}
          index={index}
          isMoreThanTwoCauses={numOfCauses >= 2}
          isLeftBorder={isLeftBorder}
          isVerySmallScreen={isVerySmallScreen}
        />
      </Stack>
      <Stack
        width={isVerySmallScreen ? "100%" : { md: "100%", xs: "50%" }}
        sx={{ position: "relative" }}
      >
        <Box
          component="img"
          sx={{
            width: { md: "390px", sm: "205px" }
          }}
          src={value.potentialCondition.conditionImage || ""}
          alt="conditionImage"
        />
      </Stack>
    </Stack>
  );
}

interface LayoutBResultSectionProps {
  value: RiskInfo;
  isDetailed: boolean;
  isVerySmallScreen: boolean;
  handleEditCause?: (outcome: string, updatedTitle: string, updatedText: string | string[]) => void;
  handleEditRecommendation?: (outcome: string, updatedTitle: string, updatedText: string | string[]) => void;
  mediaAttachments?: any[];
  caseId?: string | number;
  reportId?: string | number;
  bodyPart?: string;
  onUpdateImage?: (imageUrl: string, index: number) => void;
  showEditables: boolean;
  showProductPrice: boolean;
  enableProductLink: boolean;
}
const LayoutBResultSection = ({
  value,
  isDetailed,
  isVerySmallScreen,
  handleEditCause,
  handleEditRecommendation,
  mediaAttachments,
  caseId,
  reportId,
  bodyPart,
  onUpdateImage,
  showEditables,
  showProductPrice,
  enableProductLink,
}: LayoutBResultSectionProps) => {
  // 2 items; 1 cause, 1 recommendation, 0 product
  if (value.causedBys.length == 1 && value.productRecommendations.length == 0) {
    return (
      <Stack direction={"row"}>
        <Grid container>
          {/* adaptive view for device width > 900px */}
          <Grid display={{ xs: "none", md: "block" }} item xs={12}>
            {value.causedBys.map((causes, index) => (
              <Stack key={index + causes.title}>
                <SectionTitle title="ERGO RISKS" />

                <ResultItem
                  image={causes.image}
                  title={causes.title}
                  text={causes.text}
                  isCross={true}
                  index={index}
                  outcome={causes.outcome}
                  onSave={handleEditCause}
                  mediaAttachments={mediaAttachments} // Add this
                  caseId={caseId} // Add this
                  reportId={reportId}
                  bodyPart={bodyPart}
                  onUpdateImage={(newImage) => onUpdateImage?.(newImage, index)} // Add this
                  showEditables={showEditables}
                />
              </Stack>
            ))}
            {value.recommendations.map((recommendation, index) => (
              <Stack key={index + recommendation.title} marginTop={"30px"}>
                <SectionTitle title="RECOMMENDATIONS" />
                <ResultItem
                  image={recommendation.image}
                  title={recommendation.title}
                  text={recommendation.text}
                  isCross={false}
                  index={index}
                  outcome={recommendation.outcome}
                  onSave={handleEditRecommendation}
                  showEditables={showEditables}
                />
              </Stack>
            ))}
          </Grid>
          {/* adaptive view for device width > 900px */}

          {/* adaptive view for device width < 900px */}
          <Stack display={{ xs: "block", md: "none" }}>
            {isDetailed ? <DetailedView
              value={value}
              handleEditCause={handleEditCause}
              handleEditRecommendation={handleEditRecommendation}
              showEditables={showEditables}
              showProductPrice={showProductPrice}
              enableProductLink={enableProductLink}
            /> : isVerySmallScreen ? <VerySmallScreen
              value={value}
              isDetailed={isDetailed}
              handleEditCause={handleEditCause}
              handleEditRecommendation={handleEditRecommendation}
              showEditables={showEditables}
              showProductPrice={showProductPrice}
              enableProductLink={enableProductLink}
            /> : <SnapshotView
              value={value}
              isDetailed={isDetailed}
              handleEditCause={handleEditCause}
              handleEditRecommendation={handleEditRecommendation}
              showEditables={showEditables}
              showProductPrice={showProductPrice}
              enableProductLink={enableProductLink}
            />}
          </Stack>
          {/* adaptive view for device width < 900px */}
        </Grid>
      </Stack>
    );
  }
  // 3-4 items; 1 cause, 1 recommendation, 1-2 product
  else if (
    value.causedBys.length == 1 &&
    (value.productRecommendations.length == 1 ||
      value.productRecommendations.length == 2)
  ) {
    return (
      <>
        {/* adaptive view for device width > 900px */}
        <Stack display={{ xs: "none", md: "block" }} direction={"row"}>
          <Grid container spacing={4}>
            {value.causedBys.map((causes, index) => (
              <Grid key={index + causes.title} item xs={12} md={12} lg={6}>
                <SectionTitle title="ERGO RISKS" />
                <ResultItem
                  image={causes.image}
                  title={causes.title}
                  text={causes.text}
                  isCross={true}
                  index={index}
                  isRow={false}
                  outcome={causes.outcome}
                  onSave={handleEditCause}
                  mediaAttachments={mediaAttachments}
                  caseId={caseId}
                  reportId={reportId}
                  bodyPart={bodyPart}
                  onUpdateImage={(newImage) => onUpdateImage?.(newImage, index)}
                  showEditables={showEditables}
                />
              </Grid>
            ))}
            {value.recommendations.map((recommendation, index) => (
              <Grid
                key={index + recommendation.title}
                item
                xs={12}
                md={12}
                lg={6}
              >
                <SectionTitle title="RECOMMENDATIONS" />
                <ResultItem
                  image={recommendation.image}
                  title={recommendation.title}
                  text={recommendation.text}
                  isCross={false}
                  index={index}
                  isRow={false}
                  outcome={recommendation.outcome}
                  onSave={handleEditRecommendation}
                  showEditables={showEditables}
                />
              </Grid>
            ))}
            <Grid item>
              <SectionTitle title="PRODUCT SUGGESTIONS" />
              <Grid container spacing={3}>
                {value.productRecommendations.map((product, index) => (
                  <Grid key={index + product.name} item xs={12} md={12} lg={6}>
                    <ProductItem
                      product={product}
                      showProductPrice={showProductPrice}
                      enableProductLink={enableProductLink}
                    />
                  </Grid>
                ))}
              </Grid>
            </Grid>
          </Grid>
        </Stack>
        {/* adaptive view for device width > 900px */}

        {/* adaptive view for device width < 900px */}
        <Stack display={{ xs: "block", md: "none" }}>
          {isDetailed ? <DetailedView
            value={value}
            handleEditCause={handleEditCause}
            handleEditRecommendation={handleEditRecommendation}
            showEditables={showEditables}
            showProductPrice={showProductPrice}
            enableProductLink={enableProductLink}
          /> : isVerySmallScreen ? <VerySmallScreen
            value={value}
            isDetailed={isDetailed}
            handleEditCause={handleEditCause}
            handleEditRecommendation={handleEditRecommendation}
            showEditables={showEditables}
            showProductPrice={showProductPrice}
            enableProductLink={enableProductLink}
          /> : <SnapshotView
            value={value}
            isDetailed={isDetailed}
            handleEditCause={handleEditCause}
            handleEditRecommendation={handleEditRecommendation}
            showEditables={showEditables}
            showProductPrice={showProductPrice}
            enableProductLink={enableProductLink}
          />}
        </Stack>
        {/* adaptive view for device width < 900px */}
      </>
    );
  }
  // 4-5 items; 2 cause, 2 recommedation, 0-1 product
  else {
    return (
      <>
        {/* adaptive view for device width > 900px */}
        <Stack display={{ xs: "none", md: "block" }}>
          <Grid container spacing={4}>
            <Grid item xs={6}>
              <SectionTitle title="ERGO RISKS" />
            </Grid>
            <Grid item xs={6}>
              <SectionTitle title="RECOMMENDATIONS" />
            </Grid>
          </Grid>
          <Stack gap={"30px"}>
            {value.causedBys.map((causes, index) => (
              <Grid key={index + causes.title} container spacing={4}>
                <Grid key={index + causes.title} item xs={6}>
                  <ResultItem
                    image={causes.image}
                    title={causes.title}
                    text={causes.text}
                    isCross={true}
                    index={index}
                    isRow={false}
                    outcome={causes.outcome}
                    onSave={handleEditCause}
                    mediaAttachments={mediaAttachments}
                    caseId={caseId}
                    reportId={reportId}
                    bodyPart={bodyPart}
                    onUpdateImage={(newImage) =>
                      onUpdateImage?.(newImage, index)
                    }
                    showEditables={showEditables}
                  />
                </Grid>
                <Grid
                  key={index + value.recommendations[index].title}
                  item
                  xs={6}
                >
                  <ResultItem
                    image={value.recommendations[index].image}
                    title={value.recommendations[index].title}
                    text={value.recommendations[index].text}
                    isCross={false}
                    index={index}
                    isRow={false}
                    outcome={value.recommendations[index].outcome}
                    onSave={handleEditRecommendation}
                    showEditables={showEditables}
                  />
                </Grid>
              </Grid>
            ))}

            <Stack>
              <Grid container spacing={4} marginBottom={"20px"}>
                {value.productRecommendations.length == 0 ? (
                  <></>
                ) : (
                  <Grid item xs={6}>
                    <MLTypography
                      variant="body1"
                      fontSize={{ md: "24px", xs: "15px" }}
                      fontWeight={600}
                      lineHeight={1.2}
                    >
                      PRODUCT SUGGESTIONS
                    </MLTypography>
                  </Grid>
                )}
              </Grid>
              <Grid container spacing={3}>
                {value.productRecommendations.map((product, index) => (
                  <Grid key={index + product.name} item xs={6}>
                    <ProductItem
                      product={product}
                      showProductPrice={showProductPrice}
                      enableProductLink={enableProductLink}
                    />
                  </Grid>
                ))}
              </Grid>
            </Stack>
          </Stack>
        </Stack>
        {/* adaptive view for device width > 900px */}

        {/* adaptive view for device width < 900px */}
        <Stack display={{ xs: "block", md: "none" }}>
          {isDetailed ? <DetailedView
            value={value}
            handleEditCause={handleEditCause}
            handleEditRecommendation={handleEditRecommendation}
            showEditables={showEditables}
            showProductPrice={showProductPrice}
            enableProductLink={enableProductLink}
          />
            : isVerySmallScreen ? <VerySmallScreen
              value={value}
              isDetailed={isDetailed}
              handleEditCause={handleEditCause}
              handleEditRecommendation={handleEditRecommendation}
              showEditables={showEditables}
              showProductPrice={showProductPrice}
              enableProductLink={enableProductLink}
            />
              : <SnapshotView
                value={value}
                isDetailed={isDetailed}
                handleEditCause={handleEditCause}
                handleEditRecommendation={handleEditRecommendation}
                showEditables={showEditables}
                showProductPrice={showProductPrice}
                enableProductLink={enableProductLink}
              />}
        </Stack >
        {/* adaptive view for device width < 900px */}
      </>
    );
  }
};

// for 2 or less causes
interface LayoutBProps {
  bodyPart: string,
  value: RiskInfo,
  index: number,
  isLeftBorder: boolean,
  isDetailed: boolean,
  isCollapsedSections: { [sectionKey: string]: boolean },
  isVerySmallScreen: boolean,
  handleCollapseToggle: (section: string) => void,
  handleEditCause?: (outcome: string, updatedTitle: string, updatedText: string | string[]) => void,
  handleEditRecommendation?: (outcome: string, updatedTitle: string, updatedText: string | string[]) => void,
  mediaAttachments?: any[],
  caseId?: string | number,
  reportId?: number,
  onUpdateImage?: (imageUrl: string, index: number) => void,
  showEditables?: boolean,
  showProductPrice: boolean;
  enableProductLink: boolean;
}
export const LayoutB = ({
  bodyPart,
  value,
  index,
  isLeftBorder,
  isDetailed,
  isCollapsedSections,
  isVerySmallScreen,
  handleCollapseToggle,
  handleEditCause,
  handleEditRecommendation,
  mediaAttachments,
  caseId,
  reportId,
  onUpdateImage,
  showEditables = false,
  showProductPrice,
  enableProductLink,
}: LayoutBProps) => {
  return (
    <ContentContainer
      isLeftBorder={isLeftBorder}
    >
      <MLContainer sx={{ paddingX: "40px" }}>
        <Grid
          container
          direction={isLeftBorder ? "row" : "row-reverse"}
          spacing={2}
        >
          {value.causedBys.length == 1 &&
            value.productRecommendations.length == 0 ? (
            <>
              {" "}
              {/* 2 items; 1 cause, 1 recommendation, 0 product */}
              <Grid item xs={12} md={12} lg={5} xl={6}>
                <LayoutBHeaderSection
                  bodyPart={bodyPart}
                  value={value}
                  index={index}
                  isRow={true}
                  numOfCauses={value.causedBys.length}
                  isVerySmallScreen={isVerySmallScreen}
                />
              </Grid>
              <Grid display={{ xs: "block", md: "none" }} item xs={12} md={12} lg={6}>
                <CollapseButton
                  bodyPart={bodyPart}
                  index={index}
                  isCollapsedSections={isCollapsedSections}
                  handleCollapseToggle={handleCollapseToggle}
                />
              </Grid>
              <Grid item xs={12} md={12} lg={7} xl={6}>
                <Stack display={{ xs: "none", md: "block" }}>
                  <LayoutBResultSection
                    value={value}
                    isDetailed={isDetailed}
                    isVerySmallScreen={isVerySmallScreen}
                    handleEditCause={handleEditCause}
                    handleEditRecommendation={handleEditRecommendation}
                    mediaAttachments={mediaAttachments}
                    caseId={caseId}
                    reportId={reportId}
                    bodyPart={bodyPart}
                    onUpdateImage={onUpdateImage}
                    showEditables={showEditables}
                    showProductPrice={showProductPrice}
                    enableProductLink={enableProductLink}
                  />
                </Stack>
                <Stack display={{ xs: "block", md: "none" }}>
                  <Collapse in={isCollapsedSections[bodyPart + index]}>
                    <LayoutBResultSection
                      value={value}
                      isDetailed={isDetailed}
                      isVerySmallScreen={isVerySmallScreen}
                      handleEditCause={handleEditCause}
                      handleEditRecommendation={handleEditRecommendation}
                      mediaAttachments={mediaAttachments}
                      caseId={caseId}
                      reportId={reportId}
                      bodyPart={bodyPart}
                      onUpdateImage={onUpdateImage}
                      showEditables={showEditables}
                      showProductPrice={showProductPrice}
                      enableProductLink={enableProductLink}
                    />
                  </Collapse>
                </Stack>
              </Grid>
            </>
          ) : (
            <>
              {" "}
              {/* 3-5 items; 1-2 cause, 1-2 recommendation, 1-2 product */}
              <Grid item xs={12} md={12} lg={4}>
                <LayoutBHeaderSection
                  bodyPart={bodyPart}
                  value={value}
                  index={index}
                  isRow={undefined}
                  numOfCauses={value.causedBys.length}
                  isVerySmallScreen={isVerySmallScreen}
                />
              </Grid>
              <Grid display={{ xs: "block", md: "none" }} item xs={12} md={12} lg={6}>
                <CollapseButton
                  bodyPart={bodyPart}
                  index={index}
                  isCollapsedSections={isCollapsedSections}
                  handleCollapseToggle={handleCollapseToggle}
                />
              </Grid>
              <Grid item xs={12} md={12} lg={8}>
                <Stack display={{ xs: "none", md: "block" }}>
                  <LayoutBResultSection
                    value={value}
                    isDetailed={isDetailed}
                    isVerySmallScreen={isVerySmallScreen}
                    handleEditCause={handleEditCause}
                    handleEditRecommendation={handleEditRecommendation}
                    mediaAttachments={mediaAttachments}
                    caseId={caseId}
                    reportId={reportId}
                    bodyPart={bodyPart}
                    onUpdateImage={onUpdateImage}
                    showEditables={showEditables}
                    showProductPrice={showProductPrice}
                    enableProductLink={enableProductLink}
                  />
                </Stack>
                <Stack display={{ xs: "block", md: "none" }}>
                  <Collapse in={isCollapsedSections[bodyPart + index]}>
                    <LayoutBResultSection
                      value={value}
                      isDetailed={isDetailed}
                      isVerySmallScreen={isVerySmallScreen}
                      handleEditCause={handleEditCause}
                      handleEditRecommendation={handleEditRecommendation}
                      mediaAttachments={mediaAttachments}
                      caseId={caseId}
                      reportId={reportId}
                      bodyPart={bodyPart}
                      onUpdateImage={onUpdateImage}
                      showEditables={showEditables}
                      showProductPrice={showProductPrice}
                      enableProductLink={enableProductLink}
                    />
                  </Collapse>
                </Stack>
              </Grid>
            </>
          )}
        </Grid>
      </MLContainer>
    </ContentContainer>
  );

};

// ends here

interface ScoreProgressBarProps {
  score: number;
}
const ScoreProgressBar = ({ score = 0 }: ScoreProgressBarProps) => {
  score = score >= 99 ? 100 : score

  return (
    <Box sx={{ position: "relative" }}>
      <Box
        sx={{
          height: "35px",
          borderRadius: "20px",
          backgroundColor: "#EAEAEA",
          overflow: "hidden",
        }}
      >
        <Box
          sx={{
            width: `${score}%`,
            height: "100%",
            borderRadius: "20px",
            backgroundColor: "#31C100",
            transition: "width 0.6s ease-in-out",
          }}
        />
      </Box>

      <Box
        sx={{
          position: "absolute",
          top: "-8px",
          left: { md: `${score - 3}%`, xs: `${score - 6}%` },
          transform: "translateX(-50%)",
          color: "black",
          padding: "8px 14px",
          borderRadius: "22px",
          transition: "left 0.6s ease-in-out",
          zIndex: 1,
        }}
      >
        <MLTypography variant="subtitle1" fontWeight={500}>
          {score}
        </MLTypography>
      </Box>

      <MLTypography
        variant="subtitle1"
        marginTop={"10px"}
        fontWeight={500}
        lineHeight={1.2}
      >
        Ergo Score: {score}/100
      </MLTypography>
    </Box>
  );
};

type ActionPlanItemProps = {
  title: string;
  text: string | string[];
  points: number;
  img: string;
  isCompleted: boolean;
  outcome: string;
  handleActionPlanComplete: (outcome: string, points: number) => void;
  handleEditActionsPlan?: (outcome: string, updatedTitle: string, updatedText: string | string[]) => void;
  showActionDone?: boolean;
  showEditables?: boolean;
  itemNo: number;
  handleNeedHelp?: () => void;
};

export const ActionPlanItem: React.FC<ActionPlanItemProps> = ({
  title: initialTitle,
  text: initialText,
  points,
  img,
  isCompleted,
  outcome,
  handleActionPlanComplete,
  handleEditActionsPlan,
  showActionDone = true,
  showEditables = false,
  itemNo,
  handleNeedHelp
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [title, setTitle] = useState(initialTitle);
  const [text, setText] = useState(initialText);

  const handleSaveClick = () => {
    handleEditActionsPlan?.(outcome, title, text);
    setIsEditing(false);
  };

  const handleCancelClick = () => {
    setTitle(initialTitle);
    setText(initialText);
    setIsEditing(false);
  }
  return (
    <Stack paddingY={"20px"}>
      <Stack
        direction={{ xs: "column", sm: "row" }}
        gap={{ lg: "20px", xs: "10px" }}
        sx={{
          alignItems: "flex-start",
        }}
      >
        <Stack
          sx={{
            maxWidth: "270px",
          }}
        >
          <Box
            component="img"
            sx={{
              borderStyle: "solid",
              borderWidth: "0.5px",
              borderColor: "#C1C1C1",
              backgroundColor: "#fcfcfc",
              borderRadius: "8px",
              overflow: "hidden",
              height: { md: "181px", xs: "fit-content" },
              width: { md: "255px", xs: "auto" },
              objectFit: "contain",
            }}
            src={img || ""}
            alt="actionPlanFix"
          />
        </Stack>
        <Stack gap={"10px"}>
          <Stack direction={"row"}
            gap={"10px"}
            sx={{
              alignItems: "center",
            }}
          >
            {
              !isEditing ?
                <MLTypography variant='body1' fontSize={"16px"} fontWeight={600} lineHeight={1.2}>
                  {itemNo}. {title}
                </MLTypography>
                :
                <MLInputbox
                  label=""
                  multiline
                  value={title}
                  onChange={(e: any) => setTitle(e.target.value)}
                  sx={{ width: "100%" }}
                />
            }
            {isCompleted ? (
              <Box
                component="img"
                sx={{
                  height: "25px",
                  width: "25px",
                  objectFit: "contain",
                }}
                src={tick}
              />
            ) : (
              <></>
            )}
          </Stack>
          <Stack
            direction={"column"}
            gap={"15px"}
            height={"100%"}
            sx={{
              justifyContent: "space-between",
            }}
          >
            <Stack>
              {
                isEditing ? (
                  <MLInputbox
                    label=""
                    multiline
                    value={typeof text === "string" ? text : text.join("\n")}
                    onChange={(e: any) =>
                      setText(typeof text === "string" ? e.target.value : e.target.value.split("\n"))
                    }
                    fullWidth
                  />
                ) : (
                  typeof text === "string" ? (
                    <ul style={{ paddingLeft: "18px", marginBlock: 0 }}>
                      <MLTypography
                        component="div"
                        variant='body1'
                        fontSize={{ md: "16px", xs: "14px" }}
                        fontWeight={400}
                        lineHeight={1.2}
                        sx={{
                          "& > div > ul > li": {
                            marginBottom: "8px"
                          },
                          "& > div > ul > li:last-child": {
                            marginBottom: 0,
                          }
                        }}
                      >
                        <div dangerouslySetInnerHTML={{ __html: text }} />
                      </MLTypography>
                    </ul>
                  ) : (
                    <ul style={{ paddingLeft: "18px", marginBlock: 0 }}>
                      {text.map((textPoint, key) => (
                        <li key={textPoint + key}>
                          <MLTypography
                            component="div"
                            variant='body1'
                            fontSize={{ md: "16px", xs: "14px" }}
                            fontWeight={400}
                            lineHeight={1.2}
                            sx={{
                              "& > div > ul > li": {
                                marginBottom: "8px"
                              },
                              "& > div > ul > li:last-child": {
                                marginBottom: 0,
                              }
                            }}
                          >
                            {textPoint}
                          </MLTypography>
                        </li>
                      ))}
                    </ul>
                  )
                )}
            </Stack>
            <Stack
              direction={"column"}
              sx={{
                alignItems: { xs: "center", sm: "flex-start" },
              }}
            >
              <Stack
                direction={"column"}
                display={showActionDone ? "flex" : "none"}
                sx={{
                  justifyContent: "flex-start",
                }}
              >
                <MLTypography
                  variant="body1"
                  fontSize={"14px"}
                  fontWeight={400}
                  lineHeight={1.2}
                  sx={{ mb: 2 }}
                >
                  Add{" "}
                  <span
                    style={{
                      fontWeight: 600,
                      fontSize: "16px",
                      lineHeight: 1.2,
                      color: "#31C100",
                    }}
                  >
                    {points} points
                  </span>{" "}
                  to your ergo score
                </MLTypography>

                <Stack width={{ sm: "auto", md: "100%" }}>
                  {!isCompleted ? (
                    <MLButton
                      variant="contained"
                      color="secondary"
                      onClick={() => handleActionPlanComplete(outcome, points)}
                    >
                      Mark As Done
                    </MLButton>
                  ) : (
                    <MLButton
                      variant="outlined"
                      color="secondary"
                      sx={{
                        backgroundColor: "#98E080",
                        border: "1px solid black",
                        color: "black",
                        '&:hover': {
                          backgroundColor: "#98E080",
                          border: "1px solid black",
                          color: "black"
                        }
                      }}
                    >
                      Completed
                    </MLButton>
                  )}
                </Stack>
                
                {isCompleted && (
                  <MLButton
                    variant="text"
                    color="primary"
                    onClick={() => handleActionPlanComplete(outcome, -points)}
                    sx={{
                      textTransform: "none",
                      fontSize: "14px",
                      padding: { xs: "8px 16px", sm: 0 },
                      fontWeight: 600,
                      '&:hover': {
                        backgroundColor: "transparent",
                        color: "#5E3DCF"
                      }
                    }}
                  >
                    Reset this task
                  </MLButton>
                )}

                <MLButton
                  variant="text"
                  color="primary"
                  sx={{
                    textTransform: "none",
                    fontSize: "14px",
                    padding: { xs: "8px 16px", sm: 0 },
                    fontWeight: 600,
                    '&:hover': {
                      backgroundColor: "transparent",
                      color: "#5E3DCF"
                    }
                  }}
                  onClick={() => handleNeedHelp && handleNeedHelp()}
                >
                  Need help?
                </MLButton>
              </Stack>
            </Stack>
          </Stack>
          {/* edit/cancel/save icon */}
          <Stack alignItems="start" display={showEditables ? "block" : "none"} >
            {isEditing ?
              <Box>
                <IconButton
                  size="small"
                  aria-label="save"
                  onClick={handleSaveClick}
                >
                  <Save sx={{ color: "green" }} />
                </IconButton>
                <IconButton
                  size="small"
                  aria-label="close"
                  onClick={handleCancelClick}
                >
                  <Close sx={{ color: "red" }} />
                </IconButton>
              </Box>
              :
              <MLButton
                startIcon={<Edit />}
                onClick={() => setIsEditing(true)}
              >
                Edit
              </MLButton>
            }
          </Stack>
        </Stack>
      </Stack>
    </Stack>
  );
};

interface GoodHabitSectionProps {
  goodHabits: GoodHabit[];
  isLeftBorder: boolean;
  updateGoodHabitItem?: ((index: number, updatedTitle: string, updatedText: string | string[]) => void);
  showEditables: boolean;
  hasContainer?: boolean;
}
export const GoodHabitSection = ({
  goodHabits,
  isLeftBorder,
  updateGoodHabitItem,
  showEditables,
  hasContainer
}: GoodHabitSectionProps) => {
  return (
    <>
      {goodHabits.length == 0 ? (
        <></>
      ) : (
        <ContentContainer isLeftBorder={isLeftBorder} hasContainer={hasContainer}>
          <Stack
            direction={"row"}
            gap={"10px"}
            height={{ md: "200px", xs: "150px" }}
          >
            <Stack alignSelf="flex-start" marginTop="-25px">
              <MLTypography
                variant="h1"
                //fontSize={"100px"}
                fontSize={{ md: "88px", xs: "70px" }}
                fontWeight={600}
                color={"#6b49f2"}
              >
                {goodHabits.length}
              </MLTypography>
            </Stack>
            <Stack>
              <MLTypography
                variant="h1"
                //fontSize={"48px"}
                fontSize={{ md: "56px", xs: "40px" }}
                fontWeight={600}
                marginBottom={{ md: "20px", xs: "10px" }}
              >
                Good habits
              </MLTypography>
              <MLTypography
                //fontSize={"25px"}
                fontSize={{ md: "25px", xs: "20px" }}
                fontWeight={400}
                lineHeight={1.2}
              >
                Great work! Keep up these good habits.
              </MLTypography>
            </Stack>
          </Stack>
          <Grid container spacing={4}>
            {goodHabits.map((goodhabit, index) => (
              <Grid key={index + goodhabit.title} item lg={4} md={6} xs={12}>
                <GoodHabitItem
                  index={index}
                  isRow={false}
                  goodHabitImage={goodhabit.image}
                  goodHabitTitle={goodhabit.title}
                  goodHabitText={goodhabit.text}
                  onSave={updateGoodHabitItem}
                  showEditables={showEditables}
                />
              </Grid>
            ))}
          </Grid>
        </ContentContainer>
      )}
    </>
  );
};

interface ActionPlanSectionProps {
  isLeftBorder: boolean,
  ergoPostureScore: number,
  achievedActionScore: number,
  actionPlans: IActionPlan[],
  handleActionPlanComplete: (outcome: string, points: number,) => void,
  handleEditActionsPlan?: (index: string, updatedTitle: string, updatedText: string | string[]) => void,
  showActionDone?: boolean;
  showEditables?: boolean;
  hasContainer?: boolean;
  handleNeedHelp?: () => void;
}
export const ActionPlanSection = ({
  isLeftBorder,
  ergoPostureScore,
  achievedActionScore,
  actionPlans,
  handleActionPlanComplete,
  handleEditActionsPlan,
  showActionDone,
  showEditables,
  hasContainer,
  handleNeedHelp,
}: ActionPlanSectionProps) => {
  return (
    <ContentContainer isLeftBorder={isLeftBorder} hasContainer={hasContainer}>
      <Stack
        direction={{ lg: "row", xs: "column" }}
        gap={{ lg: "50px", xs: "25px" }}
        marginBottom={"70px"}
      >
        <Stack width={{ lg: "30%", xs: "100%" }} gap={"15px"}>
          <MLTypography
            variant="h1"
            fontSize={{ md: "56px", xs: "40px" }}
            fontWeight={600}
          >
            Your Quick Action Plan
          </MLTypography>
          <MLTypography
            variant="body1"
            fontSize={"20px"}
            fontWeight={400}
            lineHeight={1.2}
          >
            Complete the actions below to improve your score and ergonomic
            health!
          </MLTypography>
        </Stack>
        <Stack width={{ lg: "70%", xs: "100%" }} marginTop={{ lg: "60px", xs: "0px" }}>
          <ScoreProgressBar score={(ergoPostureScore ?? 0) + (achievedActionScore ?? 0)} />
        </Stack>
      </Stack>
      <Stack>
        <Stack direction={"column"} gap={"20px"}>
          {(() => {
            const recommendationsToShow = (actionPlans ?? [])
              .sort((a, b) => a.order - b.order);
            return (
              <Stack>
                <MLTypography
                  variant="h1"
                  fontSize={{ md: "40px", xs: "32px" }}
                  fontWeight={500}
                  marginY={"20px"}
                >
                  Recommendations
                </MLTypography>
                <Grid container spacing={3}>
                  {recommendationsToShow.map((action, index) => (
                    <Grid item lg={6} md={12} key={index + action.title}>
                      <ActionPlanItem
                        title={action.title}
                        text={action.text}
                        points={action.points}
                        img={action.image}
                        isCompleted={action.isCompleted}
                        outcome={action.outcome}
                        handleActionPlanComplete={handleActionPlanComplete}
                        handleEditActionsPlan={handleEditActionsPlan}
                        showActionDone={showActionDone}
                        showEditables={showEditables}
                        itemNo={index + 1}
                        handleNeedHelp={handleNeedHelp}
                      />
                    </Grid>
                  ))}
                </Grid>
              </Stack>
            );
          })()}
        </Stack>
      </Stack>
    </ContentContainer>
  );
};

interface ProductSuggestionsWithTotalSectionProps {
  isLeftBorder: boolean,
  mergedProducts: IProduct[],
}
export const ProductSuggestionsWithTotalSection = ({ isLeftBorder, mergedProducts }: ProductSuggestionsWithTotalSectionProps) => {
  return (
    <ContentContainer isLeftBorder={isLeftBorder}>
      <Grid
        container
        spacing={4}
        direction={
          isLeftBorder
            ? { md: "row", xs: "column" }
            : { md: "row-reverse", xs: "column" }
        }
      >
        <Grid item xs={4}>
          <MLTypography
            variant="h1"
            fontSize={{ md: "40px", xs: "32px" }}
            fontWeight={500}
          >
            Product Suggestions
          </MLTypography>
        </Grid>
        <Grid item xs={8}>
          <Stack gap={"20px"}>
            <Stack gap={"10px"}>
              <MLTypography
                variant="body1"
                fontSize={"16px"}
                fontWeight={600}
                lineHeight={1.2}
              >
                Recieve approval update for products purchased
              </MLTypography>
              <MLTypography
                variant="body1"
                fontSize={"16px"}
                fontWeight={400}
                lineHeight={1.2}
              >
                Purchases of recommended products below will be processed upon
                approval by HR manager. The successful procurement will be
                invoiced to the department and a notification for the delviery
                will be sent to you via email.
              </MLTypography>
            </Stack>
            <Stack gap={"15px"} width={"40%"}>
              {(mergedProducts ?? []).map((product, index) => (
                <Stack
                  key={index + product.name}
                  direction={"row"}
                  sx={{
                    justifyContent: "space-between",
                  }}
                >
                  <MLTypography
                    variant="body1"
                    fontSize={"16px"}
                    fontWeight={600}
                    lineHeight={1.2}
                    color={"#7856FF"}
                  >
                    {product.name}
                  </MLTypography>
                  <MLTypography
                    variant="body1"
                    fontSize={"16px"}
                    fontWeight={600}
                    lineHeight={1.2}
                  >
                    ${product.price}
                  </MLTypography>
                </Stack>
              ))}
            </Stack>
            <Stack>
              <Divider sx={{ backgroundColor: "black" }} />
              <Stack
                direction={"row"}
                width={"40%"}
                sx={{
                  justifyContent: "space-between",
                }}
              >
                <MLTypography
                  variant="body1"
                  fontSize={"16px"}
                  fontWeight={500}
                  lineHeight={1.2}
                  marginY={"10px"}
                >
                  Total cost
                </MLTypography>
                <MLTypography
                  variant="body1"
                  fontSize={"16px"}
                  fontWeight={500}
                  lineHeight={1.2}
                  marginY={"10px"}
                >
                  $
                  {(mergedProducts ?? []).reduce(
                    (sum, product) => sum + (product.price ?? 0),
                    0,
                  )}
                </MLTypography>
              </Stack>
              <Divider sx={{ backgroundColor: "black" }} />
            </Stack>
          </Stack>
        </Grid>
      </Grid>
    </ContentContainer>
  );
};

interface ProductSuggestionsWithoutTotalSectionProps {
  isLeftBorder: boolean;
  mergedProducts: IProduct[];
  hasContainer?: boolean;
  showProductPrice: boolean;
  enableProductLink: boolean;
}
export const ProductSuggestionsWithoutTotalSection = ({
  isLeftBorder,
  mergedProducts,
  hasContainer,
  showProductPrice,
  enableProductLink,
}: ProductSuggestionsWithoutTotalSectionProps) => {
  return (
    <ContentContainer isLeftBorder={isLeftBorder} hasContainer={hasContainer}>
      <MLTypography
        variant="h1"
        fontSize={{ md: "40px", xs: "32px" }}
        fontWeight={500}
        marginBottom={"30px"}
      >
        Product Suggestions
      </MLTypography>
      <Grid container gap="30px" direction={"row"}>
        {mergedProducts.map((product, key) => (
          <Grid
            sx={{
              padding: "20px",
              borderRadius: "10px",
              backgroundColor: "#F7F6F6",
              width: {
                xs: '100%',  // Full width on mobile
                md: 'calc(50% - 15px)',  // 2 items per row on medium screens (subtract half of gap)
                lg: 'calc((100% - 60px) / 3)',  // 3 items per row on large screens (subtract 2 gaps)
              }
            }}
            key={product.name + key}
            item
          >
            <ProductItem
              product={product}
              showProductPrice={showProductPrice}
              enableProductLink={enableProductLink}
            />
          </Grid>
        ))}
      </Grid>
    </ContentContainer>
  );
};

interface ConsultScheduleSectionProps {
  isLeftBorder: boolean;
  hasContainer?: boolean;
  handleNeedHelp?: () => void;
}
export const ConsultScheduleSection = ({
  isLeftBorder,
  hasContainer = true,
  handleNeedHelp
}: ConsultScheduleSectionProps) => {
  const navigate = useNavigate()

  return (
    <ContentContainer isLeftBorder={isLeftBorder} hasContainer={hasContainer}>
      <Grid container>
        <Grid item md={6} xs={12} display={hasContainer ? "block" : "none"}>
          <MLTypography
            variant="h1"
            fontSize={{ md: "56px", xs: "40px" }}
            fontWeight={600}
            marginBottom={"50px"}
          >
            Need more help?
          </MLTypography>
          <Stack>
            <Box
              display={{ md: "block", xs: "none" }}
              component="img"
              sx={{
                width: "523px",
                height: "318px",
              }}
              src={needHelpIcon}
            />
          </Stack>
        </Grid>
        <Grid item md={hasContainer ? 6 : 12} xs={12}>
          <MLTypography
            variant="h1"
            fontSize={"40px"}
            fontWeight={500}
            marginBottom={"30px"}
            display={hasContainer ? "none" : "block"}
          >
            Need more help?
          </MLTypography>
          <Stack gap={"50px"} direction={hasContainer ? "column" : { xs: "column", md: "row" }}>

            {/** commented out "consult an ergo coach" */}
            <Stack direction={"row"} gap={{ xs: "16px", lg: "34px" }}>
              <Box
                component="img"
                sx={{
                  width: "100px",
                  height: "100px",
                }}
                src={bookSessionIcon}
              />
              <Stack>
                <MLTypography
                  variant="body1"
                  fontSize={"20px"}
                  fontWeight={600}
                  lineHeight={1.2}
                  marginBottom={"10px"}
                >
                  Ask a Question
                </MLTypography>
                <MLTypography
                  variant="body1"
                  fontSize={"16px"}
                  fontWeight={400}
                  lineHeight={1.2}
                >
                  Get personalized advice from our certified ergonomic experts. They'll help you optimize your workspace and habits for maximum comfort and productivity.
                </MLTypography>
                <Stack direction={"row"} marginTop={"25px"}>
                  <Button
                    variant="contained"
                    sx={{
                      paddingX: "20px",
                    }}
                    onClick={() => handleNeedHelp && handleNeedHelp()}
                  >
                    ASK A QUESTION
                  </Button>
                </Stack>
              </Stack>
            </Stack>

            <Stack direction={"row"} gap={{ xs: "16px", lg: "34px" }}>
              <Box
                component="img"
                sx={{
                  width: "100px",
                  height: "100px",
                }}
                src={bookSessionIcon}
              />
              <Stack>
                <MLTypography
                  variant="body1"
                  fontSize={"20px"}
                  fontWeight={600}
                  lineHeight={1.2}
                  marginBottom={"10px"}
                >
                  Schedule Expert assessment
                </MLTypography>
                <MLTypography
                  variant="body1"
                  fontSize={"16px"}
                  fontWeight={400}
                  lineHeight={1.2}
                >
                  Our experts will conduct a comprehensive evaluation of your workspace, providing detailed recommendations for improvements and potential health benefits.
                </MLTypography>
                <Stack direction={"row"} marginTop={"25px"}>
                  <Button
                    variant="contained"
                    sx={{
                      paddingX: "20px",
                    }}
                    onClick={() => navigate("/requestAssessment")}
                  >
                    BOOK A SESSION
                  </Button>
                </Stack>
              </Stack>
            </Stack>
          </Stack>
        </Grid>
      </Grid>
    </ContentContainer>
  );
}

interface ErgoSubscriptionSectionProps {
  isLeftBorder: boolean;
  hasContainer?: boolean;
}
export const ErgoSubscriptionSection = ({
  isLeftBorder,
  hasContainer,
}: ErgoSubscriptionSectionProps) => {
  return (
    <ContentContainer
      isLeftBorder={isLeftBorder}
      hasContainer={hasContainer}
      backgroundColor={"#F2F2F2"}
      hasSideHighlight={false}
    >
      <Grid container spacing={4}>
        <Grid item xs={6}>
          <MLTypography
            variant="h1"
            //fontSize={"48px"}
            fontSize={{ md: "28px", xs: "18px" }}
            fontWeight={600}
          //marginBottom={"20px"}
          >
            Ergonomic Updates Subscription
          </MLTypography>
        </Grid>
        <Grid item xs={6}>
          <Stack gap={"30px"}>
            <Stack gap={"20px"}>
              <MLTypography
                variant="body1"
                fontSize={"16px"}
                fontWeight={400}
                lineHeight={1.2}
              >
                You're currently subscribed to receive regular updates with
                clear, concise, and extremely useful ergonomic tips you can
                apply immeidiately.
              </MLTypography>
              <Stack direction={"row"}>
                <Button
                  variant="outlined"
                  sx={{
                    paddingX: "20px",
                  }}
                  onClick={() => {
                    console.log("book session");
                  }}
                >
                  UNSUBSCRIBE
                </Button>
              </Stack>
            </Stack>
          </Stack>
        </Grid>
      </Grid>
    </ContentContainer>
  );
}

interface FooterSectionProps {
  goodHabits: GoodHabit[],
  lastContentIndex: number,
  ergoPostureScore: number,
  achievedActionScore: number,
  actionPlans: IActionPlan[],
  mergedProducts: IProduct[],
  handleActionPlanComplete: (outcome: string, points: number,) => void,
  handleEditGoodHabit?: ((index: number, updatedTitle: string, updatedText: string | string[]) => void),
  handleEditActionsPlan?: (outcome: string, updatedTitle: string, updatedText: string | string[]) => void,
  showActionDone?: boolean,
  showEditables?: boolean;
  showProductPrice: boolean;
  enableProductLink: boolean;
}

export const FooterSection = ({
  goodHabits,
  lastContentIndex,
  ergoPostureScore,
  achievedActionScore,
  actionPlans,
  mergedProducts,
  handleActionPlanComplete,
  handleEditGoodHabit,
  handleEditActionsPlan,
  showActionDone = true,
  showEditables = false,
  showProductPrice,
  enableProductLink,
}: FooterSectionProps) => {
  const isLeftBorder = (lastContentIndex) % 2 === 0; // left right alternating
  const hasTotal = false;
  return (
    <>
      <GoodHabitSection goodHabits={goodHabits} isLeftBorder={isLeftBorder} showEditables={showEditables} />

      <ActionPlanSection
        isLeftBorder={!isLeftBorder}
        ergoPostureScore={ergoPostureScore}
        achievedActionScore={achievedActionScore}
        actionPlans={actionPlans}
        handleActionPlanComplete={handleActionPlanComplete}
        handleEditActionsPlan={handleEditActionsPlan}
        showActionDone={showActionDone}
        showEditables={showEditables}
      />

      {mergedProducts.length >= 1 ? hasTotal ?
        <ProductSuggestionsWithTotalSection isLeftBorder={isLeftBorder} mergedProducts={mergedProducts} />
        : <ProductSuggestionsWithoutTotalSection
          isLeftBorder={isLeftBorder}
          mergedProducts={mergedProducts}
          showProductPrice={showProductPrice}
          enableProductLink={enableProductLink}
        />
        : (<></>)}

      <ConsultScheduleSection isLeftBorder={!isLeftBorder} />

      {/* <ErgoSubscriptionSection isLeftBorder={isLeftBorder} /> */}
    </>
  );
};
