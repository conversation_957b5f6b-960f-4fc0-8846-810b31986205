import { API_URL, NEW_FORMAT_PDF_GENERATION_SERVICE_API_URL, PDF_GENERATION_SERVICE_API_URL, TOKEN_KEY } from "../../../constants";
import { BadHabit, IActionPlan, ICalcHabitsScore, IProduct, IReport, IReportData, RiskInfo, RiskLevel } from "./Report";
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';
import IDiscomfort from "../models/IDiscomfort";


export function sortBodyParts(groupedBodyPart: { [key: string]: RiskInfo; }) {
  // sorts body part by order e.g head, eyes, etc
  // Sort riskLevel by high
  const sortedPotentialRiskParts = Object.fromEntries(
    Object.entries(groupedBodyPart).sort(([keyA, valueA], [keyB, valueB]) => {
      const riskLevels = { "high": 1, "medium": 2, "low": 3 }; // Define order: high < medium

      const riskLevelDiff = riskLevels[valueA.riskLevel] - riskLevels[valueB.riskLevel];
      if (riskLevelDiff !== 0) {
        return riskLevelDiff;
      }
      return valueA.potentialCondition.order - valueB.potentialCondition.order;
    })
  );
  return sortedPotentialRiskParts;
}

export function mergeProductSuggestions(potentialRiskParts: { [bodyPart: string]: RiskInfo }) {
  let products: IProduct[] = [];

  for (const [bodyPart, value] of Object.entries(potentialRiskParts)) {
    products = [...products, ...value.productRecommendations];
  }

  const mergedProductsHash: { [productName: string]: IProduct } = {};
  products.forEach(prod => {
    if (!mergedProductsHash[prod.name]) {
      mergedProductsHash[prod.name] = {
        id: prod.id,
        name: prod.name,
        description: prod.description,
        price: prod.price,
        image: prod.image,
        url: prod.url,
        recommendationType: prod.recommendationType,
      };
    }
  })
  return Object.values(mergedProductsHash);
}

export function mergeActionPlans(
  potentialRiskParts: { [bodyPart: string]: RiskInfo },
  maxScore: number,
  calcHabitsScore: ICalcHabitsScore[],
  additionalActionPlans: any[],
) {
  let actionPlans = []
  for (const [bodyPart, value] of Object.entries(potentialRiskParts)) {
    for (const r of value.recommendations) {
      actionPlans.push(r);
    }
  }
  actionPlans = actionPlans.concat(additionalActionPlans);

  const mergedItems: { [title: string]: IActionPlan } = {};
  actionPlans.forEach(action => {
    const { title, text, image, outcome, optionConfig } = action;

    if (!mergedItems[outcome]) {
      const habit = calcHabitsScore.find(ch => ch.habit === outcome);
      const pointsPerAction = Math.round(((habit!.maxPossiblePoints - habit!.impactFactorPoints) / maxScore) * 100)
      mergedItems[outcome] = {
        title: title,
        text: text,
        image: image,
        points: pointsPerAction,
        outcome: outcome,
        isCompleted: false,
        order: 0,
        optionConfig: optionConfig,
      };
    }
  });

  /**
   * re-order action plans by
   * 1. seat pan
   * 2. arm rest
   * 3. desk height
   * 4. screen height
   * 5. screen arrangement
   * 6. keyboard
   * 7. mouse
   * 8. posture
   */
  const orderedActionPlans = Object.entries(mergedItems).map(([key, value]) => {
    const [equipment, property] = key.split('-');

    if (equipment === "chair" && property === "sittingHeight") {
      return {
        ...value,
        order: 1,
      }
    }
    if (equipment === "chair" && property === "seatPan") {
      return {
        ...value,
        order: 2,
      }
    }
    if (equipment === "chair" && property === "armRest") {
      return {
        ...value,
        order: 3,
      }
    }
    if ((equipment === "desk" || equipment === "standingDesk") && property === "height") {
      return {
        ...value,
        order: 4,
      }
    }
    if ((equipment === "screens" || equipment === "laptops") && property === "height") {
      return {
        ...value,
        order: 5,
      }
    }
    if (property === "sitting") {
      return {
        ...value,
        order: 6,
      }
    }
    if (property === "distance") {
      return {
        ...value,
        order: 7,
      }
    }
    if (equipment === "keyboardMouse") {
      return {
        ...value,
        order: 8,
      }
    }
    if (equipment === "chair" && property === "posture") {
      return {
        ...value,
        order: 9,
      }
    }
    return { // return default if none match
      ...value,
      order: 10,
    }
  })
  return orderedActionPlans;
  const mergedActions = Object.values(mergedItems).map(action => {
    action.points = Math.round(action.points);
    return action;
  });
  return Object.values(mergedActions);
}

export const generatePDF = async (
  actionPlans: any,
  achievedActionScore: number,
  ergonomicScoreText: string,
  ergoPostureScore: number,
  mergedProductSuggestions: IProduct[] | undefined,
  riskBodyParts: { [bodyPart: string]: string } | undefined,
  username: string,
  goodHabits: any,
  potentialRiskPart: { [bodyPart: string]: RiskInfo },
  reportId: number,
  uploadedImages?: string[],
  useOneToOneFormat = false,
  orgDomain?: string,
  orgLogo?: string,
) => {
  const token = localStorage.getItem(TOKEN_KEY);

  try {
    const response = await axios.post(`${useOneToOneFormat ? NEW_FORMAT_PDF_GENERATION_SERVICE_API_URL : PDF_GENERATION_SERVICE_API_URL}/api/pdf/generate`, {
      uuid: uuidv4(),
      isErgoAssessment: true,
      orgDomain: orgDomain,
      orgLogo: orgLogo,
      reportData: {
        username: username,
        actionPlans: actionPlans,
        achievedActionScore: achievedActionScore,
        ergonomicScoreText: ergonomicScoreText,
        ergoPostureScore: ergoPostureScore,
        goodHabits: goodHabits,
        mergedProducts: mergedProductSuggestions || [],
        potentialRiskPart: potentialRiskPart,
        reportGeneratedBodyParts: riskBodyParts,
        uploadedImages: uploadedImages,
        issueBasedData: transformToIssueBasedReport({
          username: username,
          actionPlans: actionPlans,
          achievedActionScore: achievedActionScore,
          ergonomicScoreText: ergonomicScoreText,
          ergoPostureScore: ergoPostureScore,
          goodHabits: goodHabits,
          mergedProducts: mergedProductSuggestions || [],
          potentialRiskPart: potentialRiskPart,
          reportGeneratedBodyParts: riskBodyParts,
          frontBodyImage: {
            id: 0,
            url: ""
          },
          backBodyImage: {
            id: 0,
            url: ""
          }
        }).issueBasedData
      }
    });
    if (response.status !== 200) throw new Error(`PDF generation failed: ${response}`);
    await axios.put(`${API_URL}/api/self-assessment-reports/${reportId}`,
      {
        data: {
          generatedPdfUrl: response.data.url
        }
      },
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );
    return response.data.url;
  } catch (error) {
    console.error("Error generating PDF:", error);
    return "Error generating PDF"; ``
  }
}

export const generateReportData = async (
  caseId: number | undefined,
  employeeName: string,
  ergonomicScoreTextData: { text: string; textContentName: string }[],
  discomfort: IDiscomfort,
) => {
  let reportData: IReport;
  const token = localStorage.getItem(TOKEN_KEY);

  try {
    const response = await axios.get(`${API_URL}/api/generate-report/${caseId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    if (response.status !== 200) throw new Error(`report generation failed: ${response}`);
    reportData = response.data;

  } catch (error) {
    console.error("Error generating PDF:", error);
    throw error;
  }

  const badHabits: BadHabit[] = reportData.badHabits;
  const newDiscomfort = { ...discomfort };

  const groupedByBodyPart: { [key: string]: RiskInfo } = badHabits.reduce((group: any, habit) => {
    const bodyPart = habit.affectedBodyPart;
    let newCondition;

    const { isDiscomfortChecked, selectedCondition, reportGenerated, ...selectedDiscomfort } = discomfort;

    if (!group[bodyPart]) {
      let hasDiscomfortSelected = false;
      const hasConditionSelected: string[] = []
      Object.keys(selectedDiscomfort).forEach(discomfort => {
        const splitBodyPart = discomfort.toLowerCase().split('-')[0]
        const bodyPartNoPlural = splitBodyPart.endsWith('s') ? splitBodyPart.slice(0, -1) : splitBodyPart
        const splitGroupBodyPart = bodyPart.toLowerCase().split(' ')[0]
        const groupBodyPartNoPlural = splitGroupBodyPart.endsWith('s') ? splitGroupBodyPart.slice(0, -1) : splitGroupBodyPart

        if (groupBodyPartNoPlural.toLowerCase() === splitBodyPart || groupBodyPartNoPlural.toLowerCase() === bodyPartNoPlural) {
          hasDiscomfortSelected = true;
        }
      });

      Object.entries(selectedCondition ?? {}).forEach(([key, value]) => {
        const splitBodyPart = value.affectsBodyPartText.toLowerCase().split(' ')[0]
        if (bodyPart.toLowerCase().split(' ')[0] === splitBodyPart && value.isSelected) {
          hasConditionSelected.push(key);
        }
      });

      group[bodyPart] = {
        potentialCondition: {},
        riskLevel: "",
        causedBys: [],
        recommendations: [],
        productRecommendations: [],
        hasDiscomfortSelected: hasDiscomfortSelected,
        hasConditionSelected: hasConditionSelected,
        lowestPostureScore: habit.postureScore,
      };
    } else {
      group[bodyPart].lowestPostureScore = Math.min(group[bodyPart].lowestPostureScore, habit.postureScore);
    }

    const currentLowestScore = group[bodyPart].lowestPostureScore;

    if (group[bodyPart].causedBys.length == 0) {
      if (currentLowestScore == 0) { // if only 1 high risk outcome, default to medium risk condition
        newCondition = habit.canHaveConditions.find(c => c.riskLevel === RiskLevel[1]);
      } else {
        if (habit.canHaveConditions?.find(c => c.riskLevel === RiskLevel[currentLowestScore])) {
          newCondition = habit.canHaveConditions.find(c => c.riskLevel === RiskLevel[currentLowestScore]);
        } else {
          newCondition = habit.canHaveConditions?.find(c => c.riskLevel === RiskLevel[currentLowestScore + 1]);
        }
      }
    } else if (group[bodyPart].causedBys.length >= 1) {
      if (habit.canHaveConditions?.find(c => c.riskLevel === RiskLevel[currentLowestScore])) {
        newCondition = habit.canHaveConditions.find(c => c.riskLevel === RiskLevel[currentLowestScore]);
      } else {
        newCondition = habit.canHaveConditions?.find(c => c.riskLevel === RiskLevel[currentLowestScore + 1]);
      }
    }

    // if high risk not found, go 1 level down; medium risk
    // if (habit.canHaveConditions?.find(c => c.riskLevel === RiskLevel[habit.postureScore])) {
    //   newCondition = habit.canHaveConditions.find(c => c.riskLevel === RiskLevel[habit.postureScore]);
    // } else {
    //   newCondition = habit.canHaveConditions?.find(c => c.riskLevel === RiskLevel[habit.postureScore + 1]);
    // }

    if (newCondition) {
      group[bodyPart].potentialCondition = {
        bodyPart: newCondition.bodyPart, // eg "elbow-left"
        order: habit.bodyPartOrder,
        conditionName: newCondition.condition, // eg "elbow pain"
        description: newCondition.description,
        conditionImage: newCondition.conditionImage,
        altConditionImage: newCondition.altConditionImage,
      };
      group[bodyPart].riskLevel = newCondition.riskLevel;
    }


    group[bodyPart].causedBys.push({
      title: habit.title,
      text: habit.currentHabit,
      image: habit.currentHabitImage,
      outcome: habit.habit,
      postureScore: habit.postureScore,
    });
    group[bodyPart].recommendations.push({
      ...habit.recommendation,
      outcome: habit.habit,
      optionConfig: habit.optionConfig ?? {},
    });
    group[bodyPart].productRecommendations.push(...(habit.productRecommendations?.diy || []), ...(habit.productRecommendations?.product || []));
    return group;
  }, {});

  let newReportGeneratedParts = {}

  Object.values(groupedByBodyPart).forEach((p) => {
    const [basePart, side] = p.potentialCondition.bodyPart.split('-');
    const newParts = ['left', 'right'].includes(side)
      ? { [`${basePart}-left-${p.riskLevel}`]: "", [`${basePart}-right-${p.riskLevel}`]: "" }
      : { [`${p.potentialCondition.bodyPart}-${p.riskLevel}`]: "" };
    newReportGeneratedParts = { ...newReportGeneratedParts, ...newParts };
  });
  newDiscomfort["reportGenerated"] = newReportGeneratedParts;


  const sortedPotentialRiskPart = sortBodyParts(groupedByBodyPart);
  const mergedActionPlans = mergeActionPlans(
    sortedPotentialRiskPart,
    reportData.maxScore,
    reportData.calcHabitsScore,
    reportData.additionalActionPlans,
  );
  const mergedProducts = mergeProductSuggestions(groupedByBodyPart);

  let ergonomicScoreText = "";
  if (ergonomicScoreTextData) {
    if (reportData.ergoPostureScore > 80) {
      ergonomicScoreText = ergonomicScoreTextData.find(item => item.textContentName === "ergonomicScore-high")!.text
    } else if (reportData.ergoPostureScore >= 50) {
      ergonomicScoreText = ergonomicScoreTextData.find(item => item.textContentName === "ergonomicScore-medium")!.text
    } else {
      ergonomicScoreText = ergonomicScoreTextData.find(item => item.textContentName === "ergonomicScore-low")!.text
    }
  }

  const newReportData: IReportData = {
    username: employeeName ?? "",
    ergoPostureScore: reportData.ergoPostureScore,
    achievedActionScore: 0,
    frontBodyImage: {
      id: 0,
      url: ""
    },
    backBodyImage: {
      id: 0,
      url: ""
    },
    potentialRiskPart: sortedPotentialRiskPart,
    goodHabits: reportData.goodHabits,
    actionPlans: mergedActionPlans,
    mergedProducts: mergedProducts || [],
    ergonomicScoreText: ergonomicScoreText,
    reportGeneratedBodyParts: newReportGeneratedParts,
  }

  newReportData.actionPlans = mergedActionPlans;
  return { newReportData, newDiscomfort };
}

export const updateAnalyticIssueResolve = async (orgName: string, employeeId: number) => {
  const currentDate = new Date().toISOString().split('T')[0];
  const token = localStorage.getItem(TOKEN_KEY);

  try {
    let analytic;
    try {
      const analyticRes = await axios.get(`${API_URL}/api/ergo-analytics?filters[date][$eq]=${currentDate}&filters[company][$eq]=${orgName}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      analytic = analyticRes.data.data;
    } catch (error: any) {
      console.error("Failed to fetch analytics data:", error.response?.data || error.message);
      throw new Error("Failed to fetch analytics data");
    }

    let employee;
    try {
      const employeeRes = await axios.get(`${API_URL}/api/employees/${employeeId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      employee = employeeRes.data.data.attributes;
    } catch (error: any) {
      console.error("Failed to fetch employee data:", error.response?.data || error.message);
      throw new Error("Failed to fetch employee data");
    }

    const updatedIssueResolvedCount = employee.issuesResolved + 1;
    try {
      await axios.put(`${API_URL}/api/employees/${employeeId}`, {
        data: {
          issuesResolved: updatedIssueResolvedCount,
        }
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });
    } catch (error: any) {
      console.error("Failed to update employee's resolved issues count:", error.response?.data || error.message);
      throw new Error("Failed to update employee data");
    }

    if (analytic.length === 0) {
      try {
        await axios.post(`${API_URL}/api/ergo-analytics`, {
          data: {
            date: currentDate,
            department: "",
            company: orgName,
            totalAssessments: 0,
            totalRisks: {
              highRisk: 0,
              mediumRisk: 0,
              lowRisk: 0,
            },
            totalDiscomforts: {
              head: 0,
              eyes: 0,
              neck: 0,
              lowerBack: 0,
              shoulders: 0,
              elbows: 0,
              forearms: 0,
              wrists: 0,
              fingers: 0,
              knees: 0,
              feet: 0,
            },
            totalCoveredEmployees: 0,
            caseDistribution: {
              selfAssessment: {
                general: 0,
                discomfort: 0
              }
            },
            issues: {
              resolved: 1,
              helpNeeded: 0,
              identified: 0,
            }
          }
        }, {
          headers: { Authorization: `Bearer ${token}` }
        });
      } catch (error: any) {
        console.error("Failed to create analytics record:", error.response?.data || error.message);
        throw new Error("Failed to create analytics record");
      }
    } else {
      try {
        const prevAnalytic = analytic[0].attributes;
        prevAnalytic.issues.resolved += 1;
        await axios.put(`${API_URL}/api/ergo-analytics/${analytic[0].id}`, {
          data: {
            ...prevAnalytic,
          }
        }, {
          headers: { Authorization: `Bearer ${token}` }
        });
      } catch (error: any) {
        console.error("Failed to update analytics record:", error.response?.data || error.message);
        throw new Error("Failed to update analytics record");
      }
    }

    return { success: true };
  } catch (error: any) {
    console.error("Error in updateAnalyticIssueResolve:", error);
    return {
      success: false,
      error: error.message || "An unknown error occurred",
      details: error.response?.data || {}
    };
  }
}

export const updateAnalyticIssueHelpNeeded = async (orgName: string) => {
  const currentDate = new Date().toISOString().split('T')[0];
  const token = localStorage.getItem(TOKEN_KEY);

  try {
    // Get analytics data
    let analytic;
    try {
      const analyticRes = await axios.get(`${API_URL}/api/ergo-analytics?filters[date][$eq]=${currentDate}&filters[company][$eq]=${orgName}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      analytic = analyticRes.data.data;
    } catch (error: any) {
      console.error("Failed to fetch analytics data:", error.response?.data || error.message);
      throw new Error("Failed to fetch analytics data");
    }

    if (analytic.length === 0) {
      try {
        await axios.post(`${API_URL}/api/ergo-analytics`, {
          data: {
            date: currentDate,
            department: "",
            company: orgName,
            totalAssessments: 0,
            totalRisks: {
              highRisk: 0,
              mediumRisk: 0,
              lowRisk: 0,
            },
            totalDiscomforts: {
              head: 0,
              eyes: 0,
              neck: 0,
              lowerBack: 0,
              shoulders: 0,
              elbows: 0,
              forearms: 0,
              wrists: 0,
              fingers: 0,
              knees: 0,
              feet: 0,
            },
            totalCoveredEmployees: 0,
            caseDistribution: {
              selfAssessment: {
                general: 0,
                discomfort: 0
              }
            },
            issues: {
              resolved: 0,
              helpNeeded: 1,
              identified: 0,
            }
          }
        }, {
          headers: { Authorization: `Bearer ${token}` }
        });
      } catch (error: any) {
        console.error("Failed to create analytics record:", error.response?.data || error.message);
        throw new Error("Failed to create analytics record");
      }
    } else {
      try {
        const prevAnalytic = analytic[0].attributes;
        prevAnalytic.issues.helpNeeded += 1;
        await axios.put(`${API_URL}/api/ergo-analytics/${analytic[0].id}`, {
          data: {
            ...prevAnalytic,
          }
        }, {
          headers: { Authorization: `Bearer ${token}` }
        });
      } catch (error: any) {
        console.error("Failed to update analytics record:", error.response?.data || error.message);
        throw new Error("Failed to update analytics record");
      }
    }

    return { success: true };
  } catch (error: any) {
    console.error("Error in updateAnalyticIssueHelpNeeded:", error);
    return {
      success: false,
      error: error.message || "An unknown error occurred",
      details: error.response?.data || {}
    };
  }
}

export const transformToIssueBasedReport = (originalData: IReportData) => {
  // Create a map to group by outcomes/issues
  const issuesMap = new Map();
  
  // First, process action plans to get issue details
  originalData.actionPlans.forEach(actionPlan => {
    issuesMap.set(actionPlan.outcome, {
      outcome: actionPlan.outcome,
      title: actionPlan.title,
      description: actionPlan.text,
      image: actionPlan.image,
      points: actionPlan.points,
      isCompleted: actionPlan.isCompleted,
      order: actionPlan.order,
      category: actionPlan.optionConfig ? 'setup' : 'product',
      riskLevel: 'medium', // Will be updated based on affected body parts
      affectedBodyParts: [],
      productRecommendations: [],
      optionConfig: actionPlan.optionConfig || {},
      recommendationTitle: actionPlan.title // Default to action plan title
    });
  });
  
  // Then process potential risk parts to find affected body parts for each issue
  Object.entries(originalData.potentialRiskPart).forEach(([bodyPartName, bodyPartData]) => {
    bodyPartData.recommendations.forEach(recommendation => {
      const outcome = recommendation.outcome;
      
      if (issuesMap.has(outcome)) {
        const issue = issuesMap.get(outcome);
        
        // Use the specific recommendation title
        issue.recommendationTitle = recommendation.title;
        
        // Update risk level to highest among affected body parts
        type RiskLevelKey = 'high' | 'medium' | 'low';
        const riskPriority: Record<RiskLevelKey, number> = { 'high': 3, 'medium': 2, 'low': 1 };
        if (riskPriority[bodyPartData.riskLevel as RiskLevelKey] > riskPriority[issue.riskLevel as RiskLevelKey]) {
          issue.riskLevel = bodyPartData.riskLevel;
        }
        
        // Add affected body part
        issue.affectedBodyParts.push({
          name: bodyPartName,
          riskLevel: bodyPartData.riskLevel,
          condition: bodyPartData.potentialCondition,
          conditionImage: bodyPartData.potentialCondition.conditionImage,
          conditionName: bodyPartData.potentialCondition.conditionName
        });
        
        // Add product recommendations (avoiding duplicates)
        bodyPartData.productRecommendations.forEach(product => {
          const existingProduct = issue.productRecommendations.find((p: { id: number; }) => p.id === product.id);
          if (!existingProduct) {
            issue.productRecommendations.push(product);
          }
        });
      }
    });
  });
  
  // Convert to array and sort by order
  const issuesArray = Array.from(issuesMap.values()).sort((a, b) => a.order - b.order);
  
  return {
    ...originalData,
    issueBasedData: issuesArray
  };
}