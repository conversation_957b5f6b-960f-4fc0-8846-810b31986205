import MLButton from "../../../../../components/ui/MLButton/MLButton";
import MLTypography from "../../../../../components/ui/MLTypography/MLTypography";
import { Slot } from "../../../../../models/custom/SchedulingTypes";
import { getTimeRangeString } from "../../../../../utils/dateTimeUtils";
import { Grid, Stack, useTheme } from "@mui/material";
import { Dispatch, useState } from "react";

interface CustomTimeslotSelectProps {
  slotsData: Slot[];
  timezone?: string;
  selectedSlot?: Date;
  setSelectedSlot: Dispatch<Date>;
  changeCalendarView: () => void;
}

export default function CustomTimeslotSelect(props: CustomTimeslotSelectProps) {
  const {
    slotsData,
    timezone,
    selectedSlot,
    setSelectedSlot,
    changeCalendarView,
  } = props;
  const [showMore, setShowMore] = useState(false);
  const theme = useTheme();
  const slotsSliced = showMore ? slotsData : slotsData.slice(0, 12);
  return (
    <Stack direction={"column"} gap={0} pt={1}>
      <MLTypography fontWeight={"medium"}>Available slots</MLTypography>
      <Grid container sx={{ userSelect: "none" }}>
        {slotsSliced.length === 0 ? (
          <Grid item xs={12}>
            <MLTypography>No slots found</MLTypography>
          </Grid>
        ) : null}
        {slotsSliced.map((slot) => {
          const timeRangeString = getTimeRangeString(slot.start, slot.end);
          const isSelected = slot.start === selectedSlot;
          return (
            <Grid
              item
              md={2}
              sm={4}
              xs={6}
              key={`${slot.start}`}
              onClick={() => setSelectedSlot(slot.start)}
              sx={{
                padding: 0.5,
              }}
            >
              <Stack
                direction="column"
                justifyContent={"space-around"}
                alignItems={"center"}
                height={"100%"}
                width={"100%"}
                sx={{
                  cursor: "pointer",
                  border: isSelected
                    ? undefined
                    : `1px solid ${theme.palette.primary.main}`,
                  backgroundColor: isSelected
                    ? theme.palette.primary.main
                    : "white",
                  color: isSelected ? "white" : "black",
                  borderRadius: 2,
                  padding: 1
                }}
              >
                <MLTypography fontWeight={"medium"}>{timeRangeString.dateString}</MLTypography>
                <MLTypography>{timeRangeString.timeString}</MLTypography>
              </Stack>
            </Grid>
          );
        })}
        {showMore || slotsSliced.length === 0 ? (
          <Grid display={{ md: "block", xs: "none" }} item xs={12}>
            <MLButton onClick={changeCalendarView}>Choose from Calendar view</MLButton>
          </Grid>
        ) : (
          <Grid item xs={12}>
            <MLButton onClick={() => setShowMore(true)}>View more</MLButton>
          </Grid>
        )}
      </Grid>
    </Stack>
  );
}
