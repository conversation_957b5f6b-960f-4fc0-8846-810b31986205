# BalanceFlo E2E Testing Suite

This directory contains end-to-end tests for the BalanceFlo application using Playwright.

## Test Suite Overview

The test suite is organized by application modules or user flows:

*   **`auth.spec.ts`**: Handles login, logout, and authentication checks.
*   **`user-journey.spec.ts`**: A high-level smoke test that verifies core navigation paths through the application (Dashboard, Self Assessment landing, Resources, My Ergo Queries).
*   **`self-assessment-survey.spec.ts`**: Provides comprehensive E2E testing for the survey-based self-assessment flow, from starting the survey to viewing the report.
*   **`self-assessment-ai.spec.ts`**: Covers the AI-based self-assessment, including photo uploads (using fixture images) and verifying the analysis process and report generation. Requires fixture images in `tests/fixtures/`.
*   **`dashboard.spec.ts`**: Focuses on elements and basic interactions available on the main dashboard.
*   **`resources.spec.ts`**: Tests the Resources page, ensuring content loads correctly for each tab (Videos, Articles, Posters).
*   **`myergoqueries.spec.ts`**: Verifies the "My Ergo Queries" page, including tab navigation and display of queries or empty states.
*   **`employeedir.spec.ts`**: Covers the Employee Directory, including display of employees, basic search, and risk filter functionality.
*   **`calendar.spec.ts`**: Tests the Calendar page, including navigation (next/previous/today) and view switching (month/week/day).
*   **`settings.spec.ts`**: Verifies the Settings and Permissions page, including its main tabs (General, Self Assessment, Expert Assessment) and the items within them.

## Structure

```
tests/
├── auth.spec.ts           # Authentication tests (login/logout)
├── calendar.spec.ts       # Calendar page tests
├── dashboard.spec.ts      # Dashboard tests
├── employeedir.spec.ts    # Employee Directory tests
├── myergoqueries.spec.ts  # My Ergo Queries page tests
├── resources.spec.ts      # Resources page tests
├── self-assessment-ai.spec.ts # AI-based Self-Assessment tests
├── self-assessment-survey.spec.ts # Survey-based Self-Assessment tests
├── settings.spec.ts       # Settings and Permissions page tests
├── user-journey.spec.ts   # Core navigation smoke tests (refactored)
├── fixtures/              # Test fixture files (e.g., for image uploads)
│   ├── sample-chair-image.png
│   └── sample-desk-image.png
├── utils/                 # Test utilities
│   ├── auth.ts            # Authentication utilities (login, logout, etc.)
│   ├── index.ts           # Main export for all utility functions
│   ├── navigation.ts      # Navigation helper functions (if any general ones exist)
│   ├── screenshot.ts      # Utility for taking consistent screenshots
│   └── self-assessment.ts # Functions specific to self-assessment flows (e.g., answering common question types, starting assessments)
├── screenshots/           # Screenshots taken during tests (directory created on demand)
└── README.md              # This file
```

## Simplified Approach

We've adopted a simplified approach to E2E testing that:

1. **Focuses on robustness**: Tests are designed to handle different application structures and environments
2. **Minimizes maintenance**: Fewer files and simpler structure means less maintenance
3. **Provides detailed feedback**: Comprehensive logging and screenshots help with debugging
4. **Handles failures gracefully**: Tests skip or adapt when features aren't available

## Setup

1. Create a `.env.test` file in the project root with the following variables:

```
TEST_BASE_URL=http://localhost:5173
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=Testing123*
```

2. Install dependencies:

```bash
npm install
```

3. Install Playwright browsers:

```bash
npx playwright install
```

4. Ensure fixture files for AI tests are present:
   - `tests/fixtures/sample-chair-image.png`
   - `tests/fixtures/sample-desk-image.png`
   (These are placeholder images used for testing AI photo uploads. If running AI tests, these files must exist.)

## Running Tests

Run all tests:

```bash
npm test
```

Run tests with UI:

```bash
npm run test:ui
```

Run tests in headed mode:

```bash
npm run headed
```

Run specific test file:

```bash
npx playwright test tests/auth.spec.ts
```

## Test Reports

After running tests, you can view the HTML report:

```bash
npx playwright show-report
```

## Adding New Tests

1. Create a new test file in the `tests` directory
2. Import the necessary utilities from `./utils/auth` and other utility files
3. Use the existing test files as templates

Example:

```typescript
import { test, expect } from '@playwright/test';
import { login, isLoggedIn } from './utils/auth';
import fs from 'fs';
import path from 'path';

// Ensure screenshots directory exists
const screenshotsDir = path.join(process.cwd(), 'tests', 'screenshots', 'my-feature');
if (!fs.existsSync(screenshotsDir)) {
  fs.mkdirSync(screenshotsDir, { recursive: true });
}

// My feature tests
test.describe('My Feature', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    const success = await login(page);

    // Skip test if login failed
    if (!success) {
      test.skip(true, 'Login failed, cannot test feature');
    }
  });

  test('should perform some action', async ({ page }) => {
    // Verify we're logged in
    const loggedIn = await isLoggedIn(page);
    expect(loggedIn).toBeTruthy();

    // Take screenshot
    await page.screenshot({ path: path.join(screenshotsDir, 'before-action.png') });

    // Perform some action
    await page.getByRole('button', { name: 'Some Action' }).click();

    // Take screenshot after action
    await page.screenshot({ path: path.join(screenshotsDir, 'after-action.png') });

    // Verify result
    expect(page.url()).toContain('/result');
  });
});
```

## Best Practices

1. **Use the authentication utilities**: Always use the `login()` and `logout()` functions for authentication.
2. **Handle failures gracefully**: Use try/catch blocks and provide helpful error messages.
3. **Take screenshots**: Take screenshots at key points to help with debugging.
4. **Use flexible selectors**: Use multiple selectors to find elements to make tests more robust.
5. **Log useful information**: Log what's happening during the test to help with debugging.
6. **Skip tests when prerequisites fail**: Use `test.skip()` when login or other prerequisites fail.
7. **Keep tests independent**: Each test should be able to run independently.
8. **Use descriptive test names**: Make it clear what each test is checking.
9. **Use environment variables**: Store credentials and URLs in environment variables.
10. **Document your tests**: Add comments to explain what each test is doing.

## CI/CD Integration

Tests are automatically run on GitHub Actions when:
- Pushing to main, master, or develop branches
- Creating a pull request to main, master, or develop branches
- Manually triggering the workflow

The test results are uploaded as artifacts and can be downloaded from the GitHub Actions page.
