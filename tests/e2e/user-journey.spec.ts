import { test, expect } from '@playwright/test';
import { login, takeScreenshot } from '../utils'; // Assuming PageNames is not used now

/**
 * High-level smoke test for core application navigation.
 */
test.describe('Core Navigation Smoke Test', () => {
  test('should navigate through primary application pages', async ({ page }) => {
    // Step 1: Login
    console.log('Step 1: Logging in');
    await login(page);
    await takeScreenshot(page, 'user-journey', '01-logged-in'); // Removed fullPage for brevity
    
    // Step 2: Verify Dashboard Landing (initial page after login)
    console.log('Step 2: Verifying Dashboard landing');
    await page.waitForURL('/', { waitUntil: 'networkidle', timeout: 10000 });
    // A general check for navigation, assuming it's present on dashboard
    await expect(page.locator('nav').first()).toBeVisible({ timeout: 10000 }); 
    await takeScreenshot(page, 'user-journey', '02-dashboard-loaded');
    
    // Step 3: Navigate to Self Assessment Page
    console.log('Step 3: Navigating to Self Assessment Page');
    await page.goto('/self-assessment', { waitUntil: 'networkidle' });
    const gettingStartedTitle = page.getByRole('heading', { name: 'Self Assessment', exact: true });
    const reportTitle = page.getByRole('banner').getByText('Self Assessment Report');
    await expect(gettingStartedTitle.or(reportTitle)).toBeVisible({ timeout: 10000 });
    await takeScreenshot(page, 'user-journey', '03-self-assessment-landing-page');
    
    // Step 4: Navigate to Resources Page
    console.log('Step 4: Navigating to Resources Page');
    await page.goto('/resources', { waitUntil: 'networkidle' });
    await expect(page.getByRole('banner').getByText('Training')).toBeVisible({ timeout: 10000 });
    await expect(page.getByRole('tab', { name: 'Ergo Videos' })).toBeVisible({ timeout: 10000 });
    await takeScreenshot(page, 'user-journey', '04-resources-page');
    
    // Step 5: Navigate to My Ergo Queries Page
    console.log('Step 5: Navigating to My Ergo Queries Page');
    await page.goto('/myergoqueries', { waitUntil: 'networkidle' });
    await expect(page.getByRole('banner').getByText('My Ergo Queries')).toBeVisible({ timeout: 10000 });
    await takeScreenshot(page, 'user-journey', '05-myergoqueries-page');
    
    // Step 6: Return to Dashboard
    console.log('Step 6: Returning to Dashboard');
    await page.goto('/', { waitUntil: 'networkidle' });
    await expect(page.locator('nav').first()).toBeVisible({ timeout: 10000 }); 
    await takeScreenshot(page, 'user-journey', '06-returned-to-dashboard');
    
    // End of smoke test
    console.log('Core navigation smoke test completed successfully');
  });
});
