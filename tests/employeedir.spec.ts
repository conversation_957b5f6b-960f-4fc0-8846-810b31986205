import { test, expect } from '@playwright/test';
import { login, takeScreenshot } from './utils'; // Assuming utils are in parent dir

test.describe('Employee Directory Page', () => {
  test.beforeEach(async ({ page }) => {
    await login(page); // Ensure the user role can access /employees
    await page.goto('/employees', { waitUntil: 'networkidle' });
    await takeScreenshot(page, 'employeedir', '00-employee-directory-loaded');
  });

  test('should display main elements of the Employee Directory page', async ({ page }) => {
    await expect(page.getByRole('heading', { name: 'Employee directory' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Import' })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Create' })).toBeVisible();
    await expect(page.getByLabel('Search for employee name')).toBeVisible();
    await takeScreenshot(page, 'employeedir', '01-static-elements-visible');
  });

  test('should display employee list or "no employees" message', async ({ page }) => {
    // Selector for the table header row (desktop view)
    const employeeTableHeader = page.locator(
        'div.MuiStack-root:has-text("Name")' +
        ':has-text("Email address")' +
        ':has-text("Risk Level")'
    ).first();
    // Selector for an employee card (mobile view, but also good as a general item check if table structure is complex)
    // This assumes MLCard is used and contains identifiable text of an employee, e.g., an email.
    const employeeCardSelector = page.locator('div.MuiPaper-root.MuiCard-root:has-text("@")').first(); // Look for a card with an email
    const noEmployeesMessage = page.getByText('No employees found!');

    // Check for either table headers (implying table view with potential data) or an employee card, or the "no employees" message
    await expect(
      employeeTableHeader.or(employeeCardSelector).or(noEmployeesMessage)
    ).toBeVisible({ timeout: 10000 });
    await takeScreenshot(page, 'employeedir', '02-employee-list-or-empty-state');
  });

  test('should filter employees based on search query', async ({ page }) => {
    const searchInput = page.getByLabel('Search for employee name');
    await searchInput.fill('NonExistentEmployeeName123');
    // Add a small delay or wait for a specific network request if applicable,
    // otherwise, wait for the "no employees" message.
    await page.waitForTimeout(1000); // Debounce time in component is ~800ms

    await expect(page.getByText('No employees found!')).toBeVisible({ timeout: 10000 });
    await takeScreenshot(page, 'employeedir', '03-search-no-results');

    await searchInput.clear();
    await searchInput.fill(''); // Explicitly fill with empty to trigger reset if needed
    await page.waitForTimeout(1000); // Debounce time

    // After clearing, expect either some employees or still "No employees" if the directory was initially empty.
    const employeeTableHeader = page.locator(
        'div.MuiStack-root:has-text("Name")' +
        ':has-text("Email address")' +
        ':has-text("Risk Level")'
    ).first();
    const employeeCardSelector = page.locator('div.MuiPaper-root.MuiCard-root:has-text("@")').first();
    const noEmployeesMessage = page.getByText('No employees found!');
    
    await expect(
      employeeTableHeader.or(employeeCardSelector).or(noEmployeesMessage)
    ).toBeVisible({ timeout: 10000 });
    await takeScreenshot(page, 'employeedir', '04-search-cleared');
  });

  test('should filter employees by risk level', async ({ page }) => {
    // Click on the "High risk" filter
    const highRiskFilterBox = page.getByText('High risk', { exact: true }).locator('xpath=ancestor::div[contains(@style, "cursor: pointer")]');
    await highRiskFilterBox.click();
    await takeScreenshot(page, 'employeedir', '05-high-risk-filter-clicked');

    // Verify the filter appears active (e.g., by checking a style that changes)
    // Playwright cannot directly test pseudo-elements like ::after.
    // We'll check the transform style as an indicator of selection.
    await expect(highRiskFilterBox).toHaveCSS('transform', /matrix\(1.02,.*\)/, { timeout: 1000 }); // matrix(1.02, 0, 0, 1.02, 0, 0)

    // Verify the list updates (shows items or "no employees" message)
    const employeeTableHeader = page.locator(
      'div.MuiStack-root:has-text("Name")' +
      ':has-text("Email address")' +
      ':has-text("Risk Level")'
    ).first();
    const employeeCardSelector = page.locator('div.MuiPaper-root.MuiCard-root:has-text("@")').first();
    const noEmployeesMessage = page.getByText('No employees found!');
    
    await expect(
      employeeTableHeader.or(employeeCardSelector).or(noEmployeesMessage)
    ).toBeVisible({ timeout: 10000 });
    await takeScreenshot(page, 'employeedir', '06-high-risk-list-or-empty');

    // Optional: Click "Total Employees" to reset/change filter
    const totalEmployeesFilterBox = page.getByText('Total Employees', { exact: true }).locator('xpath=ancestor::div[contains(@style, "cursor: pointer")]');
    await totalEmployeesFilterBox.click();
    await takeScreenshot(page, 'employeedir', '07-total-employees-filter-clicked');
    
    await expect(totalEmployeesFilterBox).toHaveCSS('transform', /matrix\(1.02,.*\)/, { timeout: 1000 });

    await expect(
      employeeTableHeader.or(employeeCardSelector).or(noEmployeesMessage) // Check list again
    ).toBeVisible({ timeout: 10000 });
  });
});
