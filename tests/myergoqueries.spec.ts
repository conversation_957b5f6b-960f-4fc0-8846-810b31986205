import { test, expect } from '@playwright/test';
import { login, takeScreenshot } from './utils'; // Assuming utils are in parent dir

test.describe('My Ergo Queries Page', () => {
  test.beforeEach(async ({ page }) => {
    await login(page);
    await page.goto('/myergoqueries', { waitUntil: 'networkidle' });
    await takeScreenshot(page, 'myergoqueries', '00-myergoqueries-page-loaded');
  });

  test('should display "All Queries" tab by default and show content', async ({ page }) => {
    await expect(page.getByRole('banner').getByText('My Ergo Queries')).toBeVisible();
    const allQueriesTab = page.getByRole('tab', { name: /All Queries \(\d+\)/i }); // Regex to match count
    await expect(allQueriesTab).toBeVisible();
    await expect(allQueriesTab).toHaveAttribute('aria-selected', 'true');

    // Check for either query items or the "no queries" message
    const queryItemSelector = 'div.MuiPaper-root:has(h6[class*="MuiTypography-subtitle1"]):has(span:text(/(Pending|Processing|Completed)/i))';
    const noQueriesMessageSelector = page.getByText("You haven't submitted any ergo queries yet."); // Or similar text

    await expect(
      page.locator(queryItemSelector).first().or(noQueriesMessageSelector)
    ).toBeVisible({ timeout: 10000 });
    await takeScreenshot(page, 'myergoqueries', '01-all-queries-tab-content');
  });

  test('should switch to "Active" tab and show content', async ({ page }) => {
    const activeTab = page.getByRole('tab', { name: /Active \(\d+\)/i });
    await activeTab.click();
    await expect(activeTab).toHaveAttribute('aria-selected', 'true');

    const queryItemSelector = 'div.MuiPaper-root:has(h6[class*="MuiTypography-subtitle1"]):has(span:text(/(Pending|Processing|Completed)/i))';
    const noQueriesMessageSelector = page.getByText("You don't have any active ergo queries at the moment.");

    await expect(
      page.locator(queryItemSelector).first().or(noQueriesMessageSelector)
    ).toBeVisible({ timeout: 10000 });
    await takeScreenshot(page, 'myergoqueries', '02-active-tab-content');
  });

  test('should switch to "Resolved" tab and show content', async ({ page }) => {
    const resolvedTab = page.getByRole('tab', { name: /Resolved \(\d+\)/i });
    await resolvedTab.click();
    await expect(resolvedTab).toHaveAttribute('aria-selected', 'true');

    const queryItemSelector = 'div.MuiPaper-root:has(h6[class*="MuiTypography-subtitle1"]):has(span:text(/(Pending|Processing|Completed)/i))';
    const noQueriesMessageSelector = page.getByText("You don't have any resolved ergo queries yet.");
    
    await expect(
      page.locator(queryItemSelector).first().or(noQueriesMessageSelector)
    ).toBeVisible({ timeout: 10000 });
    await takeScreenshot(page, 'myergoqueries', '03-resolved-tab-content');
  });
});
