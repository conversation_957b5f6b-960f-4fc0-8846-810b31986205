import { test, expect } from '@playwright/test';
import { login, takeScreenshot } from './utils'; // Assuming utils are in parent dir

test.describe('Resources Page', () => {
  test.beforeEach(async ({ page }) => {
    await login(page);
    await page.goto('/resources', { waitUntil: 'networkidle' });
    await takeScreenshot(page, 'resources', '00-resources-page-loaded');
  });

  test('should display and load content in Ergo Videos tab', async ({ page }) => {
    const videosTab = page.getByRole('tab', { name: 'Ergo Videos' });
    await videosTab.click();
    await expect(videosTab).toHaveAttribute('aria-selected', 'true');
    await takeScreenshot(page, 'resources', '01-ergo-videos-tab-selected');

    // Wait for at least one video card to be visible
    // Assuming ErgoVideoCard renders a distinctive element, e.g., a div with class 'ergo-video-card'
    // or a specific data-testid. For now, let's use a placeholder class.
    // Replace '.ergo-video-card-selector' with an actual selector for ErgoVideoCard.
    // Using a generic MuiCard-root for now as a placeholder until a specific selector can be identified.
    // Updated selector: looks for video duration text (e.g., "1:23")
    await expect(page.getByText(/\d+:\d\d/).first()).toBeVisible({ timeout: 15000 });
    await takeScreenshot(page, 'resources', '02-ergo-videos-content-loaded');

    // TODO: Add test for "View More" if applicable and time permits.
    // TODO: Add test for filtering if applicable and time permits.
  });

  test('should display and load content in Ergo Articles tab', async ({ page }) => {
    const articlesTab = page.getByRole('tab', { name: 'Ergo Articles' });
    await articlesTab.click();
    await expect(articlesTab).toHaveAttribute('aria-selected', 'true');
    await takeScreenshot(page, 'resources', '03-ergo-articles-tab-selected');

    // Wait for at least one article card
    // Replace '.ergo-article-card-selector' with an actual selector for ErgoArticleCard.
    // Using a generic MuiCard-root for now as a placeholder until a specific selector can be identified.
    // Updated selector: looks for "Read Article" button/link
    await expect(page.getByRole('link', { name: 'Read Article' }).first()).toBeVisible({ timeout: 15000 });
    await takeScreenshot(page, 'resources', '04-ergo-articles-content-loaded');

    // TODO: Add test for "View More" if applicable.
    // TODO: Add test for filtering if applicable.
  });

  test('should display and load content in Posters tab', async ({ page }) => {
    const postersTab = page.getByRole('tab', { name: 'Posters' });
    await postersTab.click();
    await expect(postersTab).toHaveAttribute('aria-selected', 'true');
    await takeScreenshot(page, 'resources', '05-posters-tab-selected');

    // Wait for at least one poster card
    // Replace '.poster-card-selector' with an actual selector for PosterCard.
    // Using a generic MuiCard-root for now as a placeholder until a specific selector can be identified.
    // Updated selector: looks for "DOWNLOAD POSTER" button
    await expect(page.getByRole('button', { name: 'DOWNLOAD POSTER' }).first()).toBeVisible({ timeout: 15000 });
    await takeScreenshot(page, 'resources', '06-posters-content-loaded');

    // TODO: Add test for "View More" if applicable.
    // TODO: Add test for filtering if applicable.
  });
});
