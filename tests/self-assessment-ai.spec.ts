import { test, expect } from '@playwright/test';
import * as path from 'path';
import { login, takeScreenshot, answerYesNoQuestion, startAssessment } from './utils';

const chairImagePath = path.join(__dirname, 'fixtures', 'sample-chair-image.jpg');
const deskImagePath = path.join(__dirname, 'fixtures', 'sample-desk-image.jpg0'); 
const standingImagePath = path.join(__dirname, 'fixtures', 'sample-standing-image.jpg'); // Optional

test.describe('AI-Based Self-Assessment', () => {
  test.beforeEach(async ({ page }) => {
    await login(page);
    await page.goto('/self-assessment', { waitUntil: 'networkidle' });
    // Initial screenshot of the /self-assessment landing page can be taken here if needed, e.g., '00-self-assessment-landing'
    // For this refactor, we'll keep it focused on the startAssessment utility.
    await startAssessment(page, 'AI');
    await page.waitForURL(/.*\/self-assessment\/ai/); // Assert landing on the AI page
    await takeScreenshot(page, 'self-assessment-ai', '01-ai-assessment-page'); // Screenshot after assessment starts
  });

  test('should complete the AI-based self-assessment', async ({ page }) => {
    // Photo Uploads (now starting from step 02 for screenshots in this block)
    // (Desktop) Sitting in chair
    const sittingPhotoArea = page.getByText('1. Sitting in chair').locator('xpath=ancestor::div[contains(@class, "MuiBox-root")]').first();
    await sittingPhotoArea.locator('input[type="file"]').setInputFiles(chairImagePath);
    await expect(sittingPhotoArea.locator('img[alt="Chair only photo preview"]')).toBeVisible({ timeout: 10000 });
    await takeScreenshot(page, 'self-assessment-ai', '02-sitting-photo-uploaded');

    // (Desktop) Working on computer
    const workingPhotoArea = page.getByText('2. Working on computer').locator('xpath=ancestor::div[contains(@class, "MuiBox-root")]').first();
    await workingPhotoArea.locator('input[type="file"]').setInputFiles(deskImagePath);
    await expect(workingPhotoArea.locator('img[alt="Desk and chair photo preview"]')).toBeVisible({ timeout: 10000 });
    await takeScreenshot(page, 'self-assessment-ai', '03-working-photo-uploaded');

    // Discomfort Question
    await page.getByText('Have any special conditions or discomfort?').locator('..').getByRole('button', { name: 'No' }).click();
    await expect(page.getByText('Have any special conditions or discomfort?').locator('..').getByRole('button', { name: 'No' })).toHaveAttribute('aria-pressed', 'true');

    // Work Setup Questions (Chair/Desk Height) - Assuming employee role
    await answerYesNoQuestion(page, 'Is your chair height adjustable?', 'Yes');
    await answerYesNoQuestion(page, 'Is your desk height adjustable?', 'Yes');

    // Accept Terms and Conditions
    await page.getByLabel('I accept the Terms and Conditions').check();
    await expect(page.getByLabel('I accept the Terms and Conditions')).toBeChecked();

    // Submit for Analysis
    await page.getByRole('button', { name: /Submit for posture analysis/i }).click();

    // Verify Progress Modal Appears
    await expect(page.getByRole('heading', { name: 'Performing expert analysis...' })).toBeVisible({ timeout: 10000 });
    await takeScreenshot(page, 'self-assessment-ai', '04-analysis-modal-visible'); // Renumbering starts from here if 01 is in beforeEach

    // Wait for analysis to complete by monitoring progress indicators.
    const viewReportButton = page.getByRole('button', { name: /View personalised report/i });
    await expect(viewReportButton).toBeEnabled({ timeout: 180000 }); // 3 minutes timeout
    await takeScreenshot(page, 'self-assessment-ai', '05-analysis-complete-modal');

    // Click "View personalised report"
    await viewReportButton.click();

    // Assert navigation to the report page.
    await page.waitForURL(/.*\/self-assessment|\/ai-assessment\/prechecks\/.*/, { timeout: 30000 });
    await takeScreenshot(page, 'self-assessment-ai', '06-report-page-loaded');

    // Perform basic report page verifications.
    // Main Title
    await expect(page.getByRole('banner').getByText('Self Assessment Report')).toBeVisible();

    // Ergo Score Text
    await expect(page.getByText('Your Ergo score:')).toBeVisible();
    await expect(page.getByText('/100')).toBeVisible();

    // Tabs (Desktop)
    await expect(page.getByRole('tab', { name: 'Quick Summary', selected: true })).toBeVisible({ timeout: 15000 });

    // Content from "Quick Summary"
    await expect(page.getByRole('heading', { name: 'Key Ergo Risks & Recommendations' })).toBeVisible();

    // Final Screenshot
    await takeScreenshot(page, 'self-assessment-ai', '07-final-ai-report-verified');
  });
});
