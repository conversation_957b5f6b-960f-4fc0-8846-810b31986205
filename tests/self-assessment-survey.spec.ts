import { test, expect, Page } from '@playwright/test';
import { login, takeScreenshot, answerYesNoQuestion, startAssessment } from './utils';

test.describe('Survey-Based Self-Assessment', () => {
  test.beforeEach(async ({ page }) => {
    await login(page);
    await page.goto('/self-assessment', { waitUntil: 'networkidle' });
    await takeScreenshot(page, 'self-assessment-survey', '01-getting-started-page');
    await startAssessment(page, 'Survey');
    await takeScreenshot(page, 'self-assessment-survey', '02-survey-started-discomfort-page');
    // TODO: Use a more specific assertion once the actual content is known
    // For Survey, it navigates to '/self-assessment/new' which is the discomfort page
    expect(page.url()).toContain('/self-assessment/new');
  });

  test('should complete the survey-based self-assessment', async ({ page }) => {
    // Discomfort Section
    // Scenario: User has no discomfort
    await page.getByLabel('I have no discomfort').check();
    await expect(page.getByLabel('I have no discomfort')).toBeChecked();
    // Assert that the conditions area is not visible
    await expect(page.getByText('Do you have any of the following conditions?')).not.toBeVisible();
    await takeScreenshot(page, 'self-assessment-survey', '03a-no-discomfort-selected');

    // Scenario: User selects a condition (after unchecking 'no discomfort')
    await page.getByLabel('I have no discomfort').uncheck();
    await expect(page.getByLabel('I have no discomfort')).not.toBeChecked();
    // Now the conditions area should be visible
    await expect(page.getByText('Do you have any of the following conditions?')).toBeVisible();

    await page.getByLabel('Headache').check();
    await expect(page.getByLabel('Headache')).toBeChecked();
    await takeScreenshot(page, 'self-assessment-survey', '03b-condition-selected');

    // Implement interaction with body map SVG to select a specific body part.
    // Interaction for "Head"
    // Click on the 'head' SVG part to select it
    await page.locator('#head').click();
    await takeScreenshot(page, 'self-assessment-survey', 'discomfort-head-svg-clicked');

    // The BodyImageCheckbox for "Head" should now be visible (the small label)
    // Click the label for "Head" to open its specific discomfort options
    const headLabelBox = page.getByText('Head', { exact: true });
    await expect(headLabelBox).toBeVisible();
    await headLabelBox.click();
    await takeScreenshot(page, 'self-assessment-survey', 'discomfort-head-options-opened');

    // Verify the discomfort question for "Head" is visible
    await expect(page.getByText(/How severe is the discomfort in your Head/i)).toBeVisible();

    // Select a radio button option (e.g., "Mild")
    await page.getByLabel('Mild', { exact: true }).check();
    await takeScreenshot(page, 'self-assessment-survey', 'discomfort-head-option-mild-selected');

    // Verify the options box closes after selection
    await expect(page.getByText(/How severe is the discomfort in your Head/i)).not.toBeVisible();
    await expect(headLabelBox).toBeVisible();

    // Interaction for "Lower back"
    // Click on the 'lower-back' SVG part to select it
    await page.locator('#lower-back').click();
    await takeScreenshot(page, 'self-assessment-survey', 'discomfort-lowerback-svg-clicked');

    // The BodyImageCheckbox for "Lower Back" should now be visible
    const lowerBackLabelBox = page.getByText('Lower Back', { exact: true });
    await expect(lowerBackLabelBox).toBeVisible();
    await lowerBackLabelBox.click();
    await takeScreenshot(page, 'self-assessment-survey', 'discomfort-lowerback-options-opened');

    // Verify the discomfort question for "Lower Back" is visible
    await expect(page.getByText(/How severe is the discomfort in your Lower Back/i)).toBeVisible();

    // Select a radio button option (e.g., "Moderate")
    await page.getByLabel('Moderate', { exact: true }).check();
    await takeScreenshot(page, 'self-assessment-survey', 'discomfort-lowerback-option-moderate-selected');

    // Verify the options box closes
    await expect(page.getByText(/How severe is the discomfort in your Lower Back/i)).not.toBeVisible();
    await expect(lowerBackLabelBox).toBeVisible();

    // Click Next to Proceed to Work Setup
    await page.getByRole('button', { name: 'Work setup' }).click();
    await page.waitForLoadState('networkidle');
    await takeScreenshot(page, 'self-assessment-survey', '03c-navigated-to-work-setup'); // Renumbered
    await expect(page.getByRole('heading', { name: 'My work setup' })).toBeVisible();

    // Work Setup Section
    // Input Devices
    await page.getByText('Mouse').click();
    const mouseCard = page.getByText('Mouse').locator('xpath=ancestor::div[contains(@style, "border-style: solid")]');
    await expect(mouseCard.locator('[data-testid="CheckedIcon"]')).toBeVisible();

    await page.getByText('Keyboard').click();
    const keyboardCard = page.getByText('Keyboard').locator('xpath=ancestor::div[contains(@style, "border-style: solid")]');
    await expect(keyboardCard.locator('[data-testid="CheckedIcon"]')).toBeVisible();

    // Chair Adjustability
    await answerYesNoQuestion(page, 'Is height adjustable?', 'Yes');
    await answerYesNoQuestion(page, 'Is seat pan adjustable?', 'No');
    await answerYesNoQuestion(page, 'Is arm rest adjustable?', 'Yes');

    // Desk Type
    const standardDeskCardContainer = page.getByText('Standard work desk', { exact: true }).locator('xpath=ancestor::div[contains(@style, "border-style: solid")]');
    const heightAdjustableDeskCardContainer = page.getByText('Height adjustable desk', { exact: true }).locator('xpath=ancestor::div[contains(@style, "border-style: solid")]');

    await heightAdjustableDeskCardContainer.click(); // Select "Height adjustable desk"
    await expect(heightAdjustableDeskCardContainer.locator('[data-testid="CheckedIcon"]')).toBeVisible();
    await expect(standardDeskCardContainer.locator('[data-testid="CheckedIcon"]')).not.toBeVisible();
    await expect(standardDeskCardContainer.locator('[data-testid="CheckOutlineIcon"]')).toBeVisible();

    // Screen/Laptop Setup
    await page.getByRole('button', { name: 'Add Screen' }).click();
    await expect(page.getByText('Screen', { exact: true })).toBeVisible();
    await page.getByRole('button', { name: 'Add Laptop' }).click();
    await expect(page.getByText('Laptop', { exact: true })).toBeVisible();
    // TODO: Add assertions for the number of screens/laptops if needed

    // Click Next to Proceed to Work Habit
    await page.getByRole('button', { name: 'Work habit' }).click();
    await page.waitForLoadState('networkidle');
    await takeScreenshot(page, 'self-assessment-survey', '04-navigated-to-work-habit'); // Renumbered
    await expect(page.getByRole('heading', { name: 'Work habit assessment' })).toBeVisible();

    // Work Habit Section
    // Answer one question from each expected group

    // Chair Group
    const chairQuestionText = "How do you usually sit on your chair?";
    const chairOptionLabel = "Upright";
    await page.getByText(chairQuestionText).locator('xpath=ancestor::div[contains(@class, "MuiStack-root")][1]').getByLabel(chairOptionLabel).click();
    await expect(page.getByText(chairQuestionText).locator('xpath=ancestor::div[contains(@class, "MuiStack-root")][1]').getByLabel(chairOptionLabel)).toHaveAttribute('aria-pressed', 'true');

    // Desk Group
    const deskQuestionText = "How do you position your Elbows, Forearms and Wrists while working at your desk?";
    const deskOptionLabel = "Straight"; // Assuming this label exists. If not, it would need to be adjusted.
    await page.getByText(deskQuestionText).locator('xpath=ancestor::div[contains(@class, "MuiStack-root")][1]').getByLabel(deskOptionLabel).click();
    await expect(page.getByText(deskQuestionText).locator('xpath=ancestor::div[contains(@class, "MuiStack-root")][1]').getByLabel(deskOptionLabel)).toHaveAttribute('aria-pressed', 'true');

    // Screen Group (assuming one screen was added)
    const screenQuestionText = "How is your Screen positioned?";
    const screenOptionLabel = "Centered";
    await page.getByText(screenQuestionText).locator('xpath=ancestor::div[contains(@class, "MuiStack-root")][1]').getByLabel(screenOptionLabel).click();
    await expect(page.getByText(screenQuestionText).locator('xpath=ancestor::div[contains(@class, "MuiStack-root")][1]').getByLabel(screenOptionLabel)).toHaveAttribute('aria-pressed', 'true');

    // Laptop Group (assuming one laptop was added)
    const laptopQuestionText = "How is your Laptop positioned?";
    const laptopOptionLabel = "Eye Level";
    await page.getByText(laptopQuestionText).locator('xpath=ancestor::div[contains(@class, "MuiStack-root")][1]').getByLabel(laptopOptionLabel).click();
    await expect(page.getByText(laptopQuestionText).locator('xpath=ancestor::div[contains(@class, "MuiStack-root")][1]').getByLabel(laptopOptionLabel)).toHaveAttribute('aria-pressed', 'true');

    // Keyboard/Mouse Group
    const keyboardMouseQuestionText = "How do you position your Keyboard and Mouse?";
    const keyboardMouseOptionLabel = "Close to body";
    await page.getByText(keyboardMouseQuestionText).locator('xpath=ancestor::div[contains(@class, "MuiStack-root")][1]').getByLabel(keyboardMouseOptionLabel).click();
    await expect(page.getByText(keyboardMouseQuestionText).locator('xpath=ancestor::div[contains(@class, "MuiStack-root")][1]').getByLabel(keyboardMouseOptionLabel)).toHaveAttribute('aria-pressed', 'true');

    // Click Next to Generate Report
    await page.getByRole('button', { name: 'Report' }).click();
    await page.waitForLoadState('networkidle', { timeout: 10000 }); // Increased timeout for report generation
    await takeScreenshot(page, 'self-assessment-survey', '05-navigated-to-report'); // Renumbered

    // Verify Report Page
    await expect(page).toHaveURL(/.*\/self-assessment/); // URL should now be back to a summary/report page under /self-assessment

    // Main Title
    await expect(page.getByRole('banner').getByText('Self Assessment Report')).toBeVisible();

    // Ergo Score Text
    await expect(page.getByText('Your Ergo score:')).toBeVisible();
    await expect(page.getByText('/100')).toBeVisible();
    // TODO: Add more specific check for ScoreMeter component if necessary.

    // Static Texts near Score
    await expect(page.getByText('Higher score = better ergonomics')).toBeVisible();
    await expect(page.getByText('What it Means?')).toBeVisible();

    // Verify Tabs (Desktop)
    await expect(page.getByRole('tab', { name: 'Quick Summary' })).toBeVisible();
    await expect(page.getByRole('tab', { name: 'Detailed Report' })).toBeVisible();
    await expect(page.getByRole('tab', { name: 'Action Plan' })).toBeVisible();
    await expect(page.getByRole('tab', { name: 'Quick Summary', selected: true })).toBeVisible();
    // TODO: Add verification for mobile tabs (ButtonBase elements) if viewport testing is expanded.

    // Verify Content from "Quick Summary" (default active tab)
    // Note: Using getByText for flexibility, as exact heading role might vary.
    await expect(page.getByText('Key Ergo Risks & Recommendations', { exact: true })).toBeVisible();
    await expect(page.getByText('Good Habits', { exact: true })).toBeVisible();


    // Verify Action Buttons
    await expect(page.getByRole('button', { name: /download pdf/i })).toBeVisible();
    await expect(page.getByRole('button', { name: 'Retake Assessment' })).toBeVisible();

    // Final Screenshot
    await takeScreenshot(page, 'self-assessment-survey', '06-final-report-summary-verified'); // Renumbered
  });
});
