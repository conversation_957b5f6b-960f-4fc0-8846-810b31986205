import { test, expect } from '@playwright/test';
import { login, takeScreenshot } from './utils';

test.describe('Settings and Permissions Page', () => {
  test.beforeEach(async ({ page }) => {
    await login(page); // Ensure the user role can access /settingsandpermissions
    await page.goto('/settingsandpermissions', { waitUntil: 'networkidle' });
    await takeScreenshot(page, 'settings', '00-settings-page-loaded');
  });

  test('should display "General" tab by default and its items', async ({ page }) => {
    await expect(page.getByRole('heading', { name: 'Setting and Permissions' })).toBeVisible();
    
    const generalTab = page.getByRole('tab', { name: 'General' });
    await expect(generalTab).toBeVisible();
    await expect(generalTab).toHaveAttribute('aria-selected', 'true');

    // Verify items in General tab
    await expect(page.getByText('Organization details', { exact: true })).toBeVisible();
    await expect(page.getByText('Subscription', { exact: true })).toBeVisible();
    await expect(page.getByText('Case Request form', { exact: true })).toBeVisible();
    await expect(page.getByText('Workspace presets', { exact: true })).toBeVisible();
    await expect(page.getByText('Case Settings', { exact: true })).toBeVisible();
    await takeScreenshot(page, 'settings', '01-general-tab-items');
  });

  test('should switch to "Self Assessment" tab and display its items', async ({ page }) => {
    const selfAssessmentTab = page.getByRole('tab', { name: 'Self Assessment' });
    await selfAssessmentTab.click();
    await expect(selfAssessmentTab).toHaveAttribute('aria-selected', 'true');
    await page.waitForTimeout(500); // Allow content to render

    // Locate the TabPanel for "Self Assessment" first.
    // The aria-controls attribute of the tab usually points to the id of the tab panel.
    const tabPanelId = await selfAssessmentTab.getAttribute('aria-controls');
    const selfAssessmentPanel = page.locator(`div[role="tabpanel"]#${tabPanelId}`);

    // Verify items in Self Assessment tab
    await expect(selfAssessmentPanel.getByText('Assessment', { exact: true })).toBeVisible();
    await expect(selfAssessmentPanel.getByText('Report', { exact: true })).toBeVisible();
    await expect(selfAssessmentPanel.getByText('Product', { exact: true })).toBeVisible();
    await expect(selfAssessmentPanel.getByText('Rule configurations', { exact: true })).toBeVisible();
    await takeScreenshot(page, 'settings', '02-self-assessment-tab-items');

    // Optional: Click one item and verify navigation
    await selfAssessmentPanel.getByText('Assessment', { exact: true }).click();
    await page.waitForURL(/.*\/settingsandpermissions\/self-assessment\/assessment/, { timeout: 10000 });
    await takeScreenshot(page, 'settings', '03-navigated-to-sa-assessment');
    
    // Navigate back for next test
    await page.goto('/settingsandpermissions', { waitUntil: 'networkidle' }); 
    // Re-click tab to ensure context for next test if necessary
    await page.getByRole('tab', { name: 'Self Assessment' }).click();
    await page.waitForTimeout(500);
  });

  test('should switch to "Expert Assessment" tab and display its items', async ({ page }) => {
    const expertAssessmentTab = page.getByRole('tab', { name: 'Expert Assessment' });
    await expertAssessmentTab.click();
    await expect(expertAssessmentTab).toHaveAttribute('aria-selected', 'true');
    await page.waitForTimeout(500); // Allow content to render

    // Locate the TabPanel for "Expert Assessment"
    const tabPanelId = await expertAssessmentTab.getAttribute('aria-controls');
    const expertAssessmentPanel = page.locator(`div[role="tabpanel"]#${tabPanelId}`);
      
    await expect(expertAssessmentPanel.getByText('Assessment', { exact: true })).toBeVisible();
    await expect(expertAssessmentPanel.getByText('Report', { exact: true })).toBeVisible();
    await expect(expertAssessmentPanel.getByText('Product', { exact: true })).toBeVisible();
    await expect(expertAssessmentPanel.getByText('Rule configurations', { exact: true })).toBeVisible();
    await takeScreenshot(page, 'settings', '04-expert-assessment-tab-items');

    // Optional: Click one item and verify navigation
    await expertAssessmentPanel.getByText('Report', { exact: true }).click();
    await page.waitForURL(/.*\/settingsandpermissions\/expert-assessment\/report/, { timeout: 10000 });
    await takeScreenshot(page, 'settings', '05-navigated-to-ea-report');
  });
});
