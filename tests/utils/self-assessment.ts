import { Page, expect } from '@playwright/test';

/**
 * Answers a question that uses an MLToggleButtonGroup with 'Yes' and 'No' options.
 * Example: "Is your chair height adjustable?" -> Click 'Yes'
 *
 * @param page The Playwright Page object.
 * @param questionText The exact text of the question.
 * @param answer The desired answer, either 'Yes' or 'No'.
 */
export async function answerYesNoQuestion(page: Page, questionText: string, answer: 'Yes' | 'No'): Promise<void> {
  const questionLocator = page.getByText(questionText, { exact: true });
  // Assuming the MLToggleButtonGroup is within the parent of the question text element.
  // Adjust the ancestor selector if the structure is different.
  const buttonGroupParent = questionLocator.locator('xpath=ancestor::div[contains(@class, "MuiStack-root") or contains(@class, "MuiBox-root")][1]');
  const answerButton = buttonGroupParent.getByRole('button', { name: answer });

  await answerButton.click();
  await expect(answerButton).toHaveAttribute('aria-pressed', 'true');
  
  // Optional: Assert that the other button is not pressed
  const otherAnswer = answer === 'Yes' ? 'No' : 'Yes';
  const otherAnswerButton = buttonGroupParent.getByRole('button', { name: otherAnswer });
  await expect(otherAnswerButton).toHaveAttribute('aria-pressed', 'false');
}

/**
 * Handles the 'Getting Started' page for self-assessments.
 * Assumes the page is already navigated to /self-assessment.
 *
 * @param page The Playwright Page object.
 * @param assessmentType The type of assessment to start, either 'Survey' or 'AI'.
 */
export async function startAssessment(page: Page, assessmentType: 'Survey' | 'AI'): Promise<void> {
  if (assessmentType === 'Survey') {
    await page.getByRole('button', { name: /survey based assessment/i }).click();
  } else if (assessmentType === 'AI') {
    await page.getByRole('button', { name: /ai based assessment/i }).click();
  } else {
    throw new Error("Invalid assessmentType provided to startAssessment utility function.");
  }

  await page.getByRole('button', { name: /begin assessment/i }).click();

  // After clicking 'BEGIN ASSESSMENT', the URL assertion depends on the type.
  // The survey flow navigates to '/self-assessment/new' then to the Discomfort page.
  // The AI flow navigates to '/self-assessment/ai'.
  // A general waitForNavigation or waitForURL that accommodates both, or a more specific check
  // after calling this function in the test, might be needed.
  // For now, let's include a basic waitForLoadState. Specific URL checks can be done in the test.
  await page.waitForLoadState('networkidle');
}
